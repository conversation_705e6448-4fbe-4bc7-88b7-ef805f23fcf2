{"name": "portal", "version": "0.0.0", "private": true, "scripts": {"dev": "dotenv -e .env.local vite", "build": "vite build", "build:prod": "vite build --mode=production", "build:stage": "vite build --mode=staging", "build:dev": "vite build --mode=development", "preview": "vite preview", "lint": "eslint . --fix --ignore-path .gitignore", "cy:open": "cypress open", "jest:unit-testing": "jest", "jest:show-report": "open-cli coverage/lcov-report/index.html", "format": "prettier --write .", "generate-docs": "jsdoc -r -c jsdoc.json --verbose"}, "dependencies": {"@mdi/font": "7.4.47", "@modyfi/vite-plugin-yaml": "1.1.1", "@vueuse/core": "13.3.0", "axios": "1.9.0", "csv": "6.3.11", "fslightbox-vue": "2.2.1", "jszip": "3.10.1", "pdfjs-dist": "4.6.82", "pinia": "3.0.2", "pinia-plugin-persistedstate": "4.3.0", "uuid": "11.1.0", "vite-plugin-node-polyfills": "0.23.0", "vue": "3.5.16", "vue-i18n": "^11.1.7", "vue-markdown-render": "2.2.1", "vue-router": "4.5.1", "vue-toastification": "2.0.0-rc.5", "vuetify": "3.8.7"}, "devDependencies": {"@babel/preset-env": "7.27.2", "@vitejs/plugin-vue": "5.2.4", "babel-jest": "29.7.0", "cypress": "14.4.0", "eslint": "9.28.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-cypress": "5.0.1", "eslint-plugin-jest": "28.12.0", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-vue": "10.1.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jsdoc": "4.0.4", "jsdoc-export-default-interop": "0.3.1", "open-cli": "8.0.0", "prettier": "3.5.3", "sass": "1.89.1", "unplugin-fonts": "1.3.1", "vite": "6.3.5", "vite-plugin-vuetify": "2.1.1", "dotenv-cli": "8.0.0"}}