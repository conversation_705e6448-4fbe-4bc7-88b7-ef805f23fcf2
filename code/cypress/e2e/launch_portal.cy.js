before(() => {
  const CLIENT_ID = Cypress.env("CLIENT_ID");
  const LAST_AUTH_USER = Cypress.env("LAST_AUTH_USER");
  const FRESH_ACCESS_TOKEN = Cypress.env("ACCESS_TOKEN_VALUE"); // update it in `cypress.env.json`

  cy.setCookie(
    `CognitoIdentityServiceProvider.${CLIENT_ID}.${LAST_AUTH_USER}.accessToken`,
    FRESH_ACCESS_TOKEN // update it in `cypress.env.json`
  );
});

describe("Going to unexisting page", () => {
  it("should launch", () => {
    cy.visit("/something");
  });

  it("should display message that page does not exist", () => {
    cy.contains("This page does not exist");
  });
});

describe("Fetching products info on Portal", () => {
  it("should fetch product info data", () => {
    cy.intercept("GET", "/products?view=1&sort=-created&page=1&size=10").as(
      "getProducts"
    );
    cy.intercept("GET", "/brandowners?size=-1").as("getBrandowners");
    cy.intercept("GET", "/recipes?size=-1").as("getRecipes");
    cy.visit("/products");
    cy.wait("@getProducts").then((intercept) => {
      const products = intercept?.response?.body?.items;
      expect(products).not.to.be.undefined;
    });
    cy.wait("@getBrandowners").then((intercept) => {
      const brandowners = intercept?.response?.body?.items;
      expect(brandowners).not.to.be.undefined;
    });
    cy.wait("@getRecipes").then((intercept) => {
      const recipes = intercept?.response?.body?.items;
      expect(recipes).not.to.be.undefined;
    });
  });

  it("should find a label Packsuite - Portal", () => {
    cy.contains("Packsuite - Portal");
  });
});
