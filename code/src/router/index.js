import { createRouter, createWebHistory } from "vue-router";
import BrandownersView from "@/views/BrandownersView.vue";
import ProductsView from "@/views/ProductsView.vue";
import ProductView from "@/views/ProductView/ProductView.vue";
import NotFoundView from "@/views/NotFoundView.vue";
import AboutView from "@/views/AboutView.vue";
import OrdersView from "@/views/OrdersView.vue";
import RecipesView from "@/views/RecipesView.vue";
import ChangelogView from "@/views/ChangelogView.vue";
import DocumentationView from "@/views/DocumentationView.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),

  routes: [
    {
      path: "/",
      redirect: "/products",
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/customers",
      name: "customers",
      component: BrandownersView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/products",
      name: "products",
      component: ProductsView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/orders",
      name: "orders",
      component: OrdersView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/recipes",
      name: "recipes",
      component: RecipesView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/products/:id",
      name: "product",
      component: ProductView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/about",
      name: "about",
      component: AboutView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/changelog",
      name: "changelog",
      component: ChangelogView,
      meta: { layout: "DefaultLayout" },
    },
    {
      path: "/docs",
      name: "Documentation",
      component: DocumentationView,
      meta: { layout: "DocumentationLayout" },
    },
    { path: "/:pathMatch(.*)*", name: "NotFound", component: NotFoundView },
  ],

  /**
   * Always scroll to the top of the page when navigating to a new route.
   *
   * @returns {{top: number}}
   */
  scrollBehavior() {
    return { top: 0 };
  },
});

export default router;
