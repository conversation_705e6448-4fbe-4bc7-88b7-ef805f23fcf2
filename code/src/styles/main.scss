@import "@/styles/base.css";

.hidden-block {
  display: none;
}

.selected-item {
  display: flex;
  flex-direction: column;
  gap: 12px;

  &__title {
    font-size: 20px;
    font-weight: 400;
  }

  &__field {
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #3d3e40;
  }

  &__label {
    font-weight: 600;
  }

  &__value {
    text-align: right;
  }
}

.icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon-item {
  cursor: pointer;

  & path {
    color: #858585;
  }
}

.item-action-modal {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &__message {
    font-size: 18px;
    margin-top: 20px;
  }
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &__placeholder {
    width: 300px;
    margin: 0 auto;
    text-align: center;
    background-color: #1976d2;
    color: white;
    padding: 1rem;
    border-radius: 5px;
  }

  &__element {
    margin: 0 auto;
  }

  &__block {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &__header {
    display: flex;
    justify-content: space-between;
  }

  &-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    list-style-type: none;

    &__row {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    &__item {
      display: flex;
      flex-direction: column;
      gap: 4px;
      width: 33.3%;
      overflow: hidden;
      &_full-width {
        width: 100%;
      }
    }
  }

  &-item {
    &__title {
      font-size: 16px;
      font-weight: 600;
    }

    &__text {
      font-size: 14px;
    }

    &__title-wrapper {
      display: flex;
      align-items: center;
      column-gap: 4px;
    }
  }

  // add transition to to add display none to the element

  &-layout {
    &__preview {
      max-width: 480px;
      height: 360px;
      background-repeat: no-repeat;
      background-position: left top;
      background-size: contain;
      position: relative;
      background-color: white;
      overflow: hidden;
    }
  }
}

.vuetify-chip-wrapper {
  .v-chip__content {
    display: block;
    max-width: 450px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.print-container {
  position: absolute;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  color: #000000;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  row-gap: 20px;
}
.pdf-section {
  padding: 10px;
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  page-break-after: always;
  &__title {
    font-size: 36px;
  }
}
.info-block {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  &__headline {
    font-size: 28px;
    font-weight: bold;
  }
  &__text {
    font-size: 28px;
  }
  &__img {
    width: fit-content;
  }
}
.state-block {
  display: flex;
  align-items: center;
  column-gap: 8px;
  &__text {
    font-size: 28px;
  }
  &__icon {
    font-size: 28px;
  }
}
.filter-block {
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #eeeeee;
  border-radius: 10px;
  &__filter-name,
  &__filter-value {
    font-size: 28px;
  }
}
.orders-table {
  width: 100%;
  border-collapse: collapse;
  th,
  td {
    padding: 24px;
    border: 1px solid #cccccc;
  }
  th {
    text-align: left;
  }
  tr:nth-child(even) {
    background-color: #f2f2f2;
  }
}
