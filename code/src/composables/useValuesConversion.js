import { ref, computed } from "vue";

/**
 * useValuesConversion - A composable that encapsulates the logic of
 * converting values between pixels, centimeters and inches.
 * The composable takes into account the effective PPI of the canvas
 * based on the width and height of the original PDF.
 * It provides computed properties for x and y coordinates in pixels,
 * centimeters and inches, and functions to convert between them.
 *
 * @returns {Object} An object containing the following properties:
 * xPixels - The x coordinate in pixels.
 * yPixels - The y coordinate in pixels.
 * xCentimeters - The x coordinate in centimeters.
 * xInches - The x coordinate in inches.
 * yCentimeters - The y coordinate in centimeters.
 * yInches - The y coordinate in inches.
 * getEffectivePPI - The effective PPI of the canvas.
 */
export default function useValuesConversion() {
  const xPixels = ref(0);
  const yPixels = ref(0);

  const effectivePPI = ref(72);

  const xCentimeters = computed({
    /**
     * Retrieves the current x coordinate in centimeters.
     *
     * Returns the x coordinate in centimeters, based on the current
     * x coordinate in pixels and the effective PPI of the canvas.
     *
     * @returns {number} The x coordinate in centimeters.
     */
    get() {
      return Number(pixelsToCentimeters(xPixels.value));
    },
    /**
     * Sets the x coordinate in centimeters.
     *
     * Sets the x coordinate in pixels, based on the given value in
     * centimeters and the effective PPI of the canvas.
     *
     * @param {number} newValue - The new x coordinate in centimeters.
     */
    set(newValue) {
      xPixels.value = centimetersToPixels(parseFloat(newValue.toFixed(10))); // Ensure precise value
    },
  });

  const yCentimeters = computed({
    /**
     * Retrieves the current y coordinate in centimeters.
     *
     * Returns the y coordinate in centimeters, based on the current
     * y coordinate in pixels and the effective PPI of the canvas.
     *
     * @returns {number} The y coordinate in centimeters.
     */
    get() {
      return Number(pixelsToCentimeters(yPixels.value));
    },
    /**
     * Sets the y coordinate in centimeters.
     *
     * Sets the y coordinate in pixels, based on the given value in
     * centimeters and the effective PPI of the canvas.
     *
     * @param {number} newValue - The new y coordinate in centimeters.
     */
    set(newValue) {
      yPixels.value = centimetersToPixels(parseFloat(newValue.toFixed(10))); // Ensure precise value
    },
  });

  const xInches = computed({
    /**
     * Retrieves the current x coordinate in inches.
     *
     * Returns the x coordinate in inches, based on the current
     * x coordinate in pixels and the effective PPI of the canvas.
     *
     * @returns {string} The x coordinate in inches.
     */
    get() {
      return pixelsToInches(xPixels.value);
    },
    /**
     * Sets the x coordinate in inches.
     *
     * Sets the x coordinate in pixels, based on the given value in
     * inches and the effective PPI of the canvas.
     *
     * @param {number} newValue - The new x coordinate in inches.
     */
    set(newValue) {
      xPixels.value = inchesToPixels(newValue);
    },
  });

  const yInches = computed({
    /**
     * Retrieves the current y coordinate in inches.
     *
     * Returns the y coordinate in inches, based on the current
     * y coordinate in pixels and the effective PPI of the canvas.
     *
     * @returns {string} The y coordinate in inches.
     */
    get() {
      return pixelsToInches(yPixels.value);
    },
    /**
     * Sets the y coordinate in inches.
     *
     * Sets the y coordinate in pixels, based on the given value in
     * inches and the effective PPI of the canvas.
     *
     * @param {number} newValue - The new y coordinate in inches.
     */
    set(newValue) {
      yPixels.value = inchesToPixels(newValue);
    },
  });

  /**
   * Converts a given value in pixels to centimeters.
   *
   * Given a value in pixels, this function converts it to
   * centimeters by dividing it by the effective PPI of the
   * canvas and multiplying it by 2.54. The result is then
   * rounded to two decimal places using the toFixed() method.
   *
   * If the input value is 0, the function simply returns 0.
   *
   * @param {number} pixels - The value in pixels to convert.
   * @returns {number} The value in centimeters, rounded to two decimal places.
   */
  function pixelsToCentimeters(pixels) {
    if (pixels !== 0) {
      // Avoid rounding until the final step
      return ((pixels / effectivePPI.value) * 2.54).toFixed(2);
    } else {
      return 0;
    }
  }

  /**
   * Converts a given value in centimeters to pixels.
   *
   * Given a value in centimeters, this function converts it to
   * pixels by multiplying it by the effective PPI of the
   * canvas and dividing it by 2.54. The result is then
   * rounded to ten decimal places using the toFixed() method.
   *
   * If the input value is 0, the function simply returns 0.
   *
   * @param {number} centimeters - The value in centimeters to convert.
   * @returns {number} The value in pixels, rounded to ten decimal places.
   */
  function centimetersToPixels(centimeters) {
    if (centimeters !== 0) {
      // Avoid rounding and increase precision
      const result = (centimeters / 2.54) * effectivePPI.value;

      return parseFloat(result.toFixed(10)); // Use higher precision
    } else {
      return 0;
    }
  }

  /**
   * Converts a given value in pixels to inches.
   *
   * Given a value in pixels, this function converts it to
   * inches by dividing it by the effective PPI of the
   * canvas. The result is then rounded to two decimal
   * places using the toFixed() method.
   *
   * If the input value is 0, the function simply returns 0.
   *
   * @param {number} pixels - The value in pixels to convert.
   * @returns {string} The value in inches, rounded to four decimal places.
   */
  function pixelsToInches(pixels) {
    if (pixels !== 0) {
      return (pixels / effectivePPI.value).toFixed(4);
    } else {
      return "0";
    }
  }

  /**
   * Converts a given value in inches to pixels.
   *
   * Given a value in inches, this function converts it to
   * pixels by multiplying it by the effective PPI of the
   * canvas. The result is then returned as is.
   *
   * If the input value is 0, the function simply returns 0.
   *
   * @param {number} inches - The value in inches to convert.
   * @returns {number} The value in pixels.
   */
  function inchesToPixels(inches) {
    if (inches !== 0) {
      return inches * effectivePPI.value;
    } else {
      return 0;
    }
  }

  /**
   * Retrieves the effective PPI (pixels per inch) value of the canvas.
   *
   * This function returns the current effective PPI value, which is used
   * for converting between different measurement units such as pixels,
   * inches, and centimeters.
   *
   * @returns {number} The effective PPI value.
   */
  function getEffectivePPI() {
    return effectivePPI.value;
  }

  return {
    xPixels,
    yPixels,
    xCentimeters,
    xInches,
    yCentimeters,
    yInches,
    getEffectivePPI,
  };
}
