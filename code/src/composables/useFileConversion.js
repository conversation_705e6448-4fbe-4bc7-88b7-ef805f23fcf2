import {useNotificationStore} from "@base-components/BaseNotifications/notifications";
import {Notification} from "@base-components/BaseNotifications/models/Notification";
import {ALERT_STATE} from "@/components/common/BaseNotifications/models/enums";

/**
 * useFileConversion - A composable that provides functionality for processing
 * and converting PDF and PNG files. It includes methods to load files
 * from blobs, convert files to base64, extract images from PDFs, and
 * retrieve image dimensions.
 *
 * @returns {Object} An object containing methods to handle file conversion
 *   and processing:
 *   - loadPDFFile: Loads a PDF file from a given Blob and returns a File object.
 *   - loadPNGFile: Loads a PNG file from a given Blob and returns a File object.
 *   - processPDFFile: Processes a PDF file to extract its layout information,
 *     including size, image URL, width, and height.
 *   - processPNGFile: Processes a PNG file to extract its layout information,
 *     including size, image URL, width, and height.
 *   - toBase64: Converts a given file to a base64 encoded string.
 *   - pdfToImage: Converts the first page of a PDF to an image and returns the
 *     image URL.
 *   - getImageSize: Retrieves the dimensions of an image given its URL.
 */
export default function useFileConversion() {
  /**
   * Loads a PDF file from a given Blob and returns a File object.
   * @async
   * @param {Blob} pdfBlob - The blob to create a PDF File from.
   * @returns {Promise<File>} A promise that resolves to a File object
   */
  const loadPDFFile = async (pdfBlob) => {
    const pdfFile = new File([pdfBlob], "example.pdf", {
      type: "application/pdf",
    });

    return pdfFile;
  };

  /**
   * Loads a PNG file from a given Blob and returns a File object.
   * @async
   * @param {Blob} pngBlob - The blob to create a PNG File from.
   * @returns {Promise<File>} A promise that resolves to a File object
   */
  const loadPNGFile = async (pngBlob) => {
    const pngFile = new File([pngBlob], "example.png", {
      type: "image/png",
    });

    return pngFile;
  };

  /**
   * Processes a PDF file to extract its layout information, including size,
   * image URL, width, and height.
   *
   * @async
   * @param {File} file - The PDF file to be processed.
   * @returns {Promise<Object>} A promise that resolves to an object containing
   * the layout information of the PDF, including:
   * - size: The size of the PDF file in bytes.
   * - url: The URL of the converted image from the first page of the PDF.
   * - width: The width of the converted image.
   * - height: The height of the converted image.
   */
  const processPDFFile = async (file, source = "", productNumber = "", pageNumber = 1) => {
    const layout = {
      url: "",
      width: 0,
      height: 0,
    };

    // Processing PDF file
    if (file) {
      layout.size = file.size;
      const pdfURL = await toBase64(file);
      try {
        const imageURL = await pdfToImage(pdfURL, pageNumber);
        layout.url = imageURL;

        const {width, height} = await getImageSize(imageURL);
        layout.width = width;
        layout.height = height;
        return layout;
      } catch (error) {
        //should show invalid pdf structure and reset the modal to upload
        const errorMsg = `"${file.name}" is not a valid PDF file. Please upload a valid PDF file.  ${productNumber == "" ? "" : "Product Number: " + productNumber}`;
        const notification = new Notification(
          errorMsg,
          ALERT_STATE.ERROR,
          source
        );
        useNotificationStore().addNotification(notification);
        return false;
      }
    }
    return layout;
  };

  /**
   * Processes a PNG file to extract its layout information, including size,
   * image URL, width, and height.
   *
   * @async
   * @param {File} file - The PNG file to be processed.
   * @returns {Promise<Object>} A promise that resolves to an object containing
   * the layout information of the PNG, including:
   * - size: The size of the PNG file in bytes.
   * - url: The URL of the converted image from the PNG file.
   * - width: The width of the converted image.
   * - height: The height of the converted image.
   */
  const processPNGFile = async (file) => {
    const layout = {};

    // Processing PNG file
    if (file) {
      layout.size = file.size;

      const imageURL = await toBase64(file);
      layout.url = imageURL;

      const {width, height} = await getImageSize(imageURL);
      layout.width = width;
      layout.height = height;
    }

    return layout;
  };

  /**
   * Converts a file to a base64 string.
   *
   * @param {File} fileInput - The file to be converted to base64.
   * @returns {Promise<string>} A promise that resolves to the base64 string of the file.
   */
  const toBase64 = (fileInput) => {
    // Converting file to base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.readAsDataURL(fileInput);

      reader.onload = () => resolve(reader.result);

      reader.onerror = (error) => reject(error);
    });
  };

  /**
   * Converts the first page of a PDF to an image.
   *
   * @async
   * @param {string} pdfUrl - The URL of the PDF to be converted.
   * @returns {Promise<string>} A promise that resolves to the image URL of the first page in JPEG format.
   */
  const pdfToImage = async (pdfUrl, pageNumber = 1) => {
    // dpw-1139 - Dynamic import of packages to avoid loading them in the main bundle
    const {getDocument, GlobalWorkerOptions} = await import("pdfjs-dist");
    GlobalWorkerOptions.workerSrc = new URL("pdfjs-dist/build/pdf.worker.mjs", import.meta.url).href;

    // Converting PDF to image
    const pdf = await getDocument(pdfUrl).promise;

    const page = await pdf.getPage(pageNumber);
    const scale = 1;
    const viewport = page.getViewport({scale});

    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");

    canvas.width = viewport.width;
    canvas.height = viewport.height;

    await page.render({
      canvasContext: context,
      viewport: viewport,
    }).promise;

    const imageUrl = canvas.toDataURL("image/jpeg");
    return imageUrl;
  };

  /**
   * Retrieves the size of an image from its URL.
   *
   * @async
   * @param {string} url - The URL of the image.
   * @returns {Promise<{width: number, height: number}>} A promise that resolves to an object with
   * the width and height of the image.
   */
  const getImageSize = async (url) => {
    // Getting image size
    return new Promise((resolve) => {
      const img = new Image();

      img.src = url;
      img.onload = () => resolve({width: img.width, height: img.height});
    });
  };

  const pageCount = async (file) => {

    const pdfURL = await toBase64(file);

    const {getDocument, GlobalWorkerOptions} = await import("pdfjs-dist");
    GlobalWorkerOptions.workerSrc = new URL("pdfjs-dist/build/pdf.worker.mjs", import.meta.url).href;

    const pdf = await getDocument(pdfURL).promise;
    // create array with every pdf pageCount
    const pages = Array.from({length: pdf.numPages}, (_, i) => i + 1);
    return pages;
  };

  return {
    loadPDFFile,
    loadPNGFile,
    processPDFFile,
    processPNGFile,
    pageCount,
    toBase64,
    pdfToImage
  };
}
