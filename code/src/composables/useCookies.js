import axios from "axios";
import { COOKIES_NAMES } from "@/constants";

const CLIENT_ID = `${import.meta.env.VITE_CLIENT_ID}`;

const refreshTokensURL = `${import.meta.env.VITE_COGNITO_URL}/oauth2/token`;

// This thing is only needed for DEV (when we can't access secure cookie with lastAuthUser)
const envLastAuthUser = `${import.meta.env.VITE_LAST_AUTH_USER}`;
// This thing is only needed for DEV (when we can't access secure cookie with refreshToken)
const envRefreshToken = `${import.meta.env.VITE_REFRESH_TOKEN}`; // Update it once a month in env

// We're setting 60 seconds reserve to guarantee that tokens won't expire during sending request
const TOKEN_EXP_RESERVE = 60000;

/**
 * Composable to work with cookies, primarily focusing on handling tokens.
 *
 * @returns {Object} An object containing the following functions:
 *   - getFreshestToken: (tokenName: string) => string | null
 *   - removeTokensAndReloadPage: () => void
 *   - handleTokenExpiration: () => Promise<void>
 */
export const useCookies = () => {
  /**
   * Returns an object containing all cookies with their decoded values.
   * Decoding is needed because cookie values are URL encoded.
   *
   * @returns {Object.<string, string>}
   */
  const getAllCookies = () => {
    return document.cookie.split(";").reduce((cookies, cookie) => {
      const [name, value] = cookie.trim().split("=").map(decodeURIComponent);

      return { ...cookies, [name]: value };
    }, {});
  };

  /**
   * Decodes a token and returns its payload as JSON object.
   * If decoding fails (e.g. due to invalid token), it returns null.
   *
   * @param {string} token - The token to decode.
   * @returns {Object | null}
   */
  const decodeToken = (token) => {
    if (!token) return null;

    try {
      const payload = token.split(".")[1];

      return JSON.parse(atob(payload));
    } catch (e) {
      console.error(`Error decoding token from cookie "${token}":`, e);

      return null;
    }
  };

  /**
   * Returns the freshest token of the given type from all cookies.
   * If multiple tokens of the same type are present, the one with the latest expiration date is returned.
   * If no tokens of the given type are present, `null` is returned.
   *
   * @param {string} tokenName - The type of token to find the freshest one of.
   * @returns {string | null}
   */
  const getFreshestToken = (tokenName) => {
    const cookies = getAllCookies();
    let latestExpiration = 0;
    let freshestToken = null;

    const lastAuthUserKey = `CognitoIdentityServiceProvider.${CLIENT_ID}.LastAuthUser`;
    let lastAuthUser = cookies[lastAuthUserKey];

    // On dev we can't access `lastAuthUser` cookie (it will be undefined), therefore we'll take it from env
    if (!lastAuthUser) {
      lastAuthUser = envLastAuthUser;
    }

    const targetKey = `CognitoIdentityServiceProvider.${CLIENT_ID}.${lastAuthUser}.${tokenName}`;

    if (tokenName === COOKIES_NAMES.REFRESH_TOKEN_NAME) {
      return cookies[targetKey]; // We can't decode refreshToken, so no point in finding freshest one
    }

    for (const [name, value] of Object.entries(cookies)) {
      if (name === targetKey) {
        const tokenPayload = decodeToken(value);

        if (
          tokenPayload &&
          tokenPayload.exp &&
          tokenPayload.exp > latestExpiration
        ) {
          latestExpiration = tokenPayload.exp;
          freshestToken = value;
        }
      }
    }

    return freshestToken;
  };

  /**
   * Sets a token as a cookie with a specified name, value, and path.
   * The expiration date of the token is determined based on the token value.
   * The domain for the cookie is set conditionally based on the hostname.
   *
   * @param {string} tokenName - The name of the token to set as a cookie.
   * @param {string} tokenValue - The value of the token to set as a cookie.
   * @param {string} [tokenPath="/"] - The path attribute for the cookie (default is "/").
   */
  const setToken = (tokenName, tokenValue, tokenPath = "/") => {
    const cookies = getAllCookies();

    let tokenExpDate = new Date(0);

    // If we have some not empty token value, we need to set its exp date value
    if (tokenValue) {
      tokenExpDate.setUTCSeconds(decodeToken(tokenValue)?.exp);
    }

    tokenExpDate = tokenExpDate.toUTCString();

    const lastAuthUserKey = `CognitoIdentityServiceProvider.${CLIENT_ID}.LastAuthUser`;
    let lastAuthUser = cookies[lastAuthUserKey];

    // On dev we can't access `lastAuthUser` cookie (it will be undefined), therefore we'll take it from env
    if (!lastAuthUser) {
      lastAuthUser = envLastAuthUser;
    }

    const targetKey = `CognitoIdentityServiceProvider.${CLIENT_ID}.${lastAuthUser}.${tokenName}`;

    // On localhost we won't set domain as `.mybhs.de`, instead we set empty string
    const cookieDomain =
      window.location.hostname === "localhost"
        ? ""
        : COOKIES_NAMES.TOKENS_DOMAIN;

    document.cookie = `${targetKey}=${tokenValue}; expires=${tokenExpDate}; domain=${cookieDomain}; path=${tokenPath};`;
  };

  /**
   * Removes all tokens and reloads the page if all of them expired.
   * This will let our lambda workflow to move user to login and refresh all the tokens.
   * The function does nothing if page reload is already in progress.
   */
  const removeTokensAndReloadPage = () => {
    if (!sessionStorage.getItem("pageReload")) {
      sessionStorage.setItem("pageReload", "true");

      // If all the tokens expired, then we need to remove them all and reload the page
      // That will let our lambda workflow to move user to login and refresh all the tokens
      setToken(COOKIES_NAMES.ACCESS_TOKEN_NAME, "");
      setToken(COOKIES_NAMES.ID_TOKEN_NAME, "");
      setToken(COOKIES_NAMES.REFRESH_TOKEN_NAME, "");

      location.reload();
    }
  };

  /**
   * Checks the expiration status of the access token. If the access token is still valid, the function does nothing.
   * If the access token is expired but the refresh token is valid, it refreshes the access token and ID token.
   * If both tokens are expired or not present, it removes all tokens and reloads the page to redirect the user to login.
   *
   * The function also handles cases where the refresh token cannot be accessed in development environments by using
   * an environment variable as a fallback.
   *
   * @async
   */
  const handleTokenExpiration = async () => {
    const accessToken = getFreshestToken(COOKIES_NAMES.ACCESS_TOKEN_NAME);
    const decodedAccessToken = decodeToken(accessToken);

    if (
      decodedAccessToken &&
      new Date(decodedAccessToken.exp * 1000 - TOKEN_EXP_RESERVE) > new Date()
    ) {
      return; // if `accessToken` is still alive, then we do nothing here
    }

    let refreshToken = getFreshestToken(COOKIES_NAMES.REFRESH_TOKEN_NAME);

    // On dev we can't access `refreshToken` cookie (it will be null), therefore we'll take it from env
    if (!refreshToken) {
      refreshToken = envRefreshToken;
    }

    // If at least refresh token is alive, we refresh accessToken and idToken
    if (refreshToken) {
      try {
        const params = new URLSearchParams();

        params.append("refresh_token", refreshToken);
        params.append("grant_type", "refresh_token");
        params.append("client_id", CLIENT_ID);

        const res = await axios.post(refreshTokensURL, params);

        setToken(COOKIES_NAMES.ACCESS_TOKEN_NAME, res.data.access_token);
        setToken(COOKIES_NAMES.ID_TOKEN_NAME, res.data.id_token);
      } catch (error) {
        console.error(`Error occurred while refreshing tokens: ${error}`);
      }
    } else {
      removeTokensAndReloadPage(); // If all the tokens expired (even refresh one), we reload the page
    }
  };

  return {
    getFreshestToken,
    removeTokensAndReloadPage,
    handleTokenExpiration,
  };
};
