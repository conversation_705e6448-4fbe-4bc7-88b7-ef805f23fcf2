import { ref } from "vue";

// Width and height of the PDF layout
const originalDimensions = ref({ width: 0, height: 0 });

/**
 * Provides methods to work with original PDF dimensions.
 *
 * @returns {Object} Object with methods to set and get original PDF
 *                   dimensions.
 */
export default function useOriginalDimensions() {
  /**
   * Sets the original dimensions of the PDF layout.
   *
   * @param {Object} value - An object containing the width and height properties
   *                         representing the original dimensions of the PDF layout.
   */
  const setOriginalDimensions = (value) => {
    originalDimensions.value = value;
  };

  /**
   * Returns the original dimensions of the PDF layout.
   *
   * @returns {Object} Object with width and height properties
   *                   representing the original dimensions of the PDF layout.
   */
  const getOriginalDimensions = () => {
    return originalDimensions.value;
  };

  return {
    setOriginalDimensions,
    getOriginalDimensions,
  };
}
