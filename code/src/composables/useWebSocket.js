import { ref, onUnmounted } from "vue";
import { COOKIES_NAMES } from "@/constants";
import { useCookies } from "@/composables/useCookies";
import {useToast} from "vue-toastification";

const { getFreshestToken } = useCookies();

export default function useWebSocket({ onMessage }) {
  const socket = ref(null);
  const isConnected = ref(false);
  let pingInterval = null;

  const connect = () => {
    const token = `${getFreshestToken(COOKIES_NAMES.ACCESS_TOKEN_NAME)}`;

    socket.value = new WebSocket(
      `${import.meta.env.VITE_WEBSOCKET_API_URL}?organization=Schwarz&token=${token}`
    );

    socket.value.onopen = () => {
      isConnected.value = true;

      pingInterval = setInterval(() => {
        if (socket.value && socket.value.readyState === WebSocket.OPEN) {
          socket.value.send(JSON.stringify({ type: "ping" }));
        }
      }, 9*60*1000);
    };

    socket.value.onmessage = (event) => {

      try {
        const data = JSON.parse(event.data);
        if (onMessage) onMessage(data);
      } catch (err) {}
    };

    socket.value.onclose = () => {
      isConnected.value = false;
      clearInterval(pingInterval);
      setTimeout(connect, 9 * 60 * 1000); // Reconnect
    };

    socket.value.onerror = (error) => {
      useToast.error(JSON.stringify(error))
    };
  };

  connect();

  onUnmounted(() => {
    clearInterval(pingInterval);
    socket.value.close();
  });

  return {
    socket,
    isConnected,
  };
}
