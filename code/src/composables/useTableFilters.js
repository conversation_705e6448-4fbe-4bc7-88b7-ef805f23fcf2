import { computed } from "vue";
import { PRODUCT_STATUS } from "@/store/products";
import { ORDER_STATUS } from "@/store/orders";
import {useBrandownersStore} from "@/store/brandowners";
import {usePlantsStore} from "@/store/plants";
import { useRecipesStore } from "@/store/recipes";

/**
 * Composable that computes an array of active filter chips, product states with their names as values and messages
 * as text, order states with their names as values and messages as text, and provides a debounced function to
 * handle changes in the filter object.
 *
 * @param {Object} currentFilters - The object containing the current filters.
 *
 * @returns {Object} An object containing the computed properties and functions.
 * - `activeFilterChips`: An array of active filter chips.
 * - `productStateOptions`: List of product states in the form of { title: string, value: string }
 * - `orderStateOptions`: List of order states in the form of { title: string, value: string }
 * - `debouncedFetchData`: Debounced function to handle changes in the filter object.
 * - `removeFilter`: Function to remove a specified filter from the current filter set.
 * - `removeAllFilters`: Function to remove all filters from the current filter set.
 */
export default function useTableFilters(currentFilters) {
  const brandownersStore = useBrandownersStore();
  const plantsStore = usePlantsStore();
  const recipesStore = useRecipesStore();

  /**
   * Helper function to resolve the display label for multi-select filter chips.
   * @param {string} filterKey The key of the filter (e.g., 'brandownersNames', 'plantNames').
   * @param {string} itemValue The value selected (e.g., brandowner ID, plant ID).
   */
  const resolveMultiSelectLabel = (filterKey, itemValue) => {
    let options = [];
    switch (filterKey) {
      case 'brandownersNames':
        options = brandownersStore.brandownersNames;
        break;
      case 'plantNames':
        options = plantsStore.plantsNames;
        break;
      case 'printerNames':
        options = plantsStore.dpusNames;
        break;
      case 'papertype':
        options = recipesStore.recipesNames;
        break;
      default:
        return itemValue;
    }

    // Find the object by its ID and return its text, or default to itemValue if not found
    const selectedOption = options.find(option => option.value === itemValue);
    return selectedOption ? selectedOption.text : itemValue;
  };

  /**
   * Computes an array of active filter chips for the products table.
   *
   * An active filter chip is an object with five properties: key, value, filterKey, filterType, and label.
   * The key is a string that uniquely identifies the filter chip. It is usually the same as the filterKey for
   * single-value filters.
   * The value is the value of the filter, which is a string, array or date object.
   * The filterKey is the key of the filter in the currentFilters object. It is used to remove the filter when
   * the chip is clicked.
   * The filterType is the type of the filter, which is one of "input", "multiSelect", or "dateInput".
   * The label is the text that is displayed on the filter chip.
   *
   * @returns {Array} An array of active filter chips.
   */
  const activeFilterChips = computed(() => {
    const result = [];

    for (const key in currentFilters) {
      const {
        value: filterValue,
        type: filterType,
        label: filterLabel,
      } = currentFilters[key];

      switch (filterType) {
        case "input": {
          if (filterValue !== "" && filterValue !== null) {
            result.push({
              key,
              value: filterValue,
              filterKey: key,
              filterType,
              label: `${filterLabel}: ${filterValue}`,
            });
          }
          break;
        }
        case "multiSelect": {
          filterValue.forEach((itemValue) => {
            const displayedLabel = resolveMultiSelectLabel(key, itemValue);
            result.push({
              key: `${key}-${itemValue}`,
              value: itemValue,
              filterKey: key,
              filterType,
              label: `${filterLabel}: ${displayedLabel}`,
            });
          });
          break;
        }
        case "dateInput": {
          if (filterValue !== null) {
            result.push({
              key,
              value: filterValue,
              filterKey: key,
              filterType,
              label: `${filterLabel}: ${new Date(filterValue).toLocaleDateString()}`,
            });
          }
          break;
        }
        default: {
          throw new Error(
            `Unknown filter type passed while computing: ${filterType}`
          );
        }
      }
    }

    return result;
  });

  /**
   * Computes a list of product states with their names as values and messages as text, to be used as filter options.
   *
   * @returns {Array} List of product states in the form of { title: string, value: string }
   */
  const productStateOptions = computed(() => {
    return Object.values(PRODUCT_STATUS).map((obj) => ({
      text: obj.message,
      value: obj.name,
    }));
  });

  /**
   * Computes a list of orders states with their names as values and messages as text, to be used as filter options.
   *
   * @returns {Array} List of product states in the form of { title: string, value: string }
   */
  const orderStateOptions = computed(() => {
    return Object.values(ORDER_STATUS).map((obj) => ({
      text: obj.message,
      value: obj.name,
    }));
  });

  /**
   * Computes a list of archived values options with their display text and boolean value.
   *
   * @returns {Array} List of archived value options in the form of { text: string, value: boolean }
   */
  const archivedValuesOptions = computed(() => {
    return [
      { text: "Activated", value: false },
      { text: "Deactivated", value: true },
    ];
  });

  /**
   * Computes a list of disabled values options with their display text and boolean value.
   *
   * @returns {Array} List of disabled value options in the form of { text: string, value: boolean }
   */
  const disabledValuesOptions = computed(() => {
    return [
      { text: "Disabled", value: true },
      { text: "Enabled", value: false },
    ];
  });

  /**
   * Removes a specified filter from the current filter set based on the filter type.
   *
   * @param {string} filterType - The type of filter to be removed, such as "input", "select",
   *                              "dateInput", or "multiSelect".
   * @param {string} filterKey - The key of the filter within the currentFilters object.
   * @param {string|array} filterValue - The value of the filter to be removed, used specifically
   *                                     for "multiSelect" filter type.
   *
   * For "input" type, the value is reset to an empty string.
   * For "select" and "dateInput" types, the value is reset to null.
   * For "multiSelect" type, the specified value is removed from the array of values.
   *
   * Throws an error if an unknown filter type is passed.
   */
  function removeFilter(filterType, filterKey, filterValue) {
    switch (filterType) {
      case "input": {
        currentFilters[filterKey].value = "";
        break;
      }
      case "dateInput": {
        currentFilters[filterKey].value = null;
        break;
      }
      case "multiSelect": {
        const indexToRemove =
          currentFilters[filterKey].value.indexOf(filterValue);
        currentFilters[filterKey].value.splice(indexToRemove, 1);
        break;
      }
      default: {
        throw new Error(
          `Unknown filter type passed while removing: ${filterType}`
        );
      }
    }
  }

  /**
   * Removes all filters from the current filter set.
   *
   * Resets values of all filters to their default states: empty string for "input" type,
   * null for "select" and "dateInput" types, and empty array for "multiSelect" type.
   *
   * Throws an error if an unknown filter type is passed.
   */
  function removeAllFilters() {
    for (const key in currentFilters) {
      switch (currentFilters[key].type) {
        case "input":
          currentFilters[key].value = "";
          break;
        case "dateInput":
          currentFilters[key].value = null;
          break;
        case "multiSelect":
          currentFilters[key].value = [];
          break;
        default:
          throw new Error(
            `Unknown filter type passed while removing all: ${this.currentFilters[key].type}`
          );
      }
    }
  }

  return {
    activeFilterChips,
    productStateOptions,
    orderStateOptions,
    archivedValuesOptions,
    disabledValuesOptions,
    removeFilter,
    removeAllFilters,
  };
}
