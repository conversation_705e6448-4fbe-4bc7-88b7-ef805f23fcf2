import { computed } from "vue";
import config from "@/assets/config.json";

/**
 * Composable function to filter and return the necessary headers for a table.
 *
 * @param {string} tableName - The name of the table to retrieve configuration for.
 * @param {Array<Object>} tableHeaders - An array of all possible header objects for the table
 *                                       that have "title" and "key" properties.
 * @returns {Object} An object containing a computed property "requiredHeaders" that filters
 *                   the input headers to include only those that are required based on the
 *                   configuration and always includes the "Actions" column.
 */
export default function useTableColumns(tableName, tableHeaders) {
  const headers = computed(() => {
    // If no configuration for the table defined, we use an empty object
    const columnsInfo = config.tablesColumns[tableName] ?? {};

    // Filter headers to include only those that are required due to configuration
    const headers = tableHeaders.filter((header) => columnsInfo[header.key]);

    // Always include the "Actions" column
    headers.push({ title: "Actions", align: "end" });

    return headers;
  });

  return {
    headers,
  };
}
