import axios from "axios";
import { useCookies } from "@/composables/useCookies";
import { COOKIES_NAMES } from "@/constants";

const { getFreshestToken, removeTokensAndReloadPage, handleTokenExpiration } =
  useCookies();

const baseURL = `${import.meta.env.VITE_PORTAL_API_URL}`;

let isTokenChecking = null; // Promise or null

/**
 * Creates a new axios instance with the base URL set to the portal API URL
 * and the content type set to the provided value or "application/json" by default.
 *
 * The instance is also configured with two interceptors.
 * The first one checks if the token is expired and refreshes it if needed.
 * The second one catches 401 errors, removes all the tokens and reloads the page.
 *
 * @param {string} [contentType] - The content type of the requests.
 * @returns {AxiosInstance} - The created axios instance.
 */
export const createAPI = (contentType) => {
  const api = axios.create({
    baseURL,
    headers: {
      "Content-Type": contentType || "application/json",
    },
  });

  api.interceptors.request.use(async (config) => {
    // If we aren't checking token from anywhere, then...
    if (!isTokenChecking) {
      isTokenChecking = handleTokenExpiration(); // Setting Promise to the variable
    }
    await isTokenChecking; // Wait before check will done (either from this request or some other request)
    isTokenChecking = null;

    const token = getFreshestToken(COOKIES_NAMES.ACCESS_TOKEN_NAME);

    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }

    return config;
  });

  api.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      // Usually, we check / refresh all the tokens before requests (even with 60 seconds reserve)
      // So, that case is only possible when refreshToken is expired, but didn't remove from cookies in time by browser
      if (error.response && error.response.status === 401) {
        // If we got 401 error then we remove all the tokens and reload the page
        // removeTokensAndReloadPage();

        // Instead, redirect to unauthorized page for testing
        console.log('401 Unauthorized - redirecting to unauthorized page');
        window.location.href = '/error/unauthorized.html';
        return;
      }

      return Promise.reject(error);
    }
  );

  return api;
};
