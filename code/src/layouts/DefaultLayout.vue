<template>
  <!-- Default layout of the application (when user is authorized) -->
  <v-app>
    <!-- Main header of the application -->
    <AppBar :title="title" :leftButtons="leftButtons" :rightButtons="rightButtons" @toggleSidebar="toggleSidebar" />

    <!-- Sidebar of the application -->
    <AppSidebar :isOpen="isSidebarOpen" @toggleSidebar="toggleSidebar" />

    <!-- Main content of the application -->
    <v-main>
      <div class="container">
        <RouterView />
      </div>
    </v-main>
  </v-app>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { RouterView } from "vue-router";
import { useWindowSize } from "@vueuse/core";
import AppBar from "@/components/AppBar.vue";
import AppSidebar from "@/components/AppSidebar.vue";

const isSidebarOpen = ref(false);
const { width } = useWindowSize();
const title = 'Packsuite - Portal';

const leftButtons = [
  {
    icon: 'mdi-menu',
    callback: () => {
      toggleSidebar()
    },
  },
];
const rightButtons = [
  {
    icon: 'mdi-logout',
    callback: () => {
      location.href = "/logout";
    },
  },
];

/**
 * Toggles the state of the sidebar between open and closed.
 */
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};

/**
 * Checks if the window width is greater than or equal to 1280px and toggles the sidebar accordingly.
 * @param {number} windowWidth - The current window width.
 */
const checkIfDesktop = (windowWidth) => {
  if (windowWidth >= 1500 && isSidebarOpen.value === false) {
    toggleSidebar();
  } else if (windowWidth < 1500 && isSidebarOpen.value === true) {
    toggleSidebar();
  }
};

watch(width, () => {
  checkIfDesktop(width.value);
});

onMounted(async () => {
  checkIfDesktop(width.value);
});
</script>
