<template>
    <!-- Default layout of the application (when user is authorized) -->
    <v-app>
        <!-- Main content of the application -->
        <v-main>
            <div class="container">
                <RouterView/>
            </div>
        </v-main>
    </v-app>
</template>

<script setup>
import { RouterView } from "vue-router";
</script>
<style scoped lang="scss">
.container {
    padding: 0px !important;
    height: 100%;
}
</style>