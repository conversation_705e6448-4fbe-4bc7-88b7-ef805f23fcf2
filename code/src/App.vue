<template>
  <!-- Main component of the application (depends on the current route) -->
  <component :is="layout">
    <router-view />
  </component>
  <ChangelogNotification />
  <modal-host ref="modalHost" />
</template>

<script setup>
import ModalHost from '@/components/modals/ModalHost.vue'
import { useRoute, RouterView } from "vue-router";
import { computed, onMounted, ref, provide, useTemplateRef } from "vue";
import UnauthorizedLayout from "@/layouts/UnauthorizedLayout";
import DocumentationLayout from "@/layouts/DocumentationLayout.vue";
import DefaultLayout from "@/layouts/DefaultLayout.vue";
import ChangelogNotification from "@/components/ChangelogNotification.vue";

const route = useRoute();
const modalHost = useTemplateRef('modalHost');

provide('openModal', (component, props) => {
  return modalHost.value?.open(component, props)
})

const layout = computed(() => {
  switch (route.meta.layout) {
    case "UnauthorizedLayout":
      return UnauthorizedLayout;
    case "DocumentationLayout":
      return DocumentationLayout;
    default:
      return DefaultLayout;
  }
});

onMounted(() => {
  sessionStorage.removeItem("pageReload");
});

</script>

<style lang="scss">
#app {
  margin: 0 !important;
  width: 100% !important;
  max-width: unset !important;
  display: block !important;
  padding: 0 !important;
  .v-main {
    background-color: #e9edf0;
    .container {
      padding: 1rem;
    }
  }
}

.v-overlay-container .v-list-item * {
  color: black !important;
  font-weight: normal !important;
}
</style>
