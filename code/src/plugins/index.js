/**
 * plugins/index.js
 *
 * Automatically included in `./src/main.js`
 */

// Plugins
import vuetify from "@/plugins/vuetify";
import pinia from "@/store";
import router from "@/router";
import Toast from "@/plugins/vue-toastification";

/**
 * Registers plugins for the Vue application instance.
 *
 * @param {Object} app - The Vue application instance.
 *
 * This function initializes the following plugins:
 * - Vuetify: A Material Design component framework.
 * - Router: Facilitates client-side routing.
 * - Pinia: A state management library.
 * - Toast: A toast notification library.
 */
export function registerPlugins(app) {
  app.use(vuetify).use(router).use(pinia).use(Toast);
}
