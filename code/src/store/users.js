import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
import { createAPI } from "@/api/index";

const api = createAPI();

const USERS_URL = `${import.meta.env.VITE_PORTAL_API_URL}/users`;

export const useUsersStore = defineStore("users", {
  /**
   * The state of the users store.
   *
   * @property {Object} users - an object mapping user IDs to user data (name and email)
   * @property {boolean} areUsersLoaded - a boolean indicating if the users array is loaded
   * @property {Object} useToast - A toast notification instance for displaying messages.
   */
  state: () => ({
    users: {},
    areUsersLoaded: false,
    useToast: useToast(),
  }),

  actions: {
    /**
     * Fetches users from the server and populates the users store.
     *
     * @param {string[]} usersIds - an array of user IDs to fetch
     * @returns {Promise<void>}
     */
    async fetchUsers(usersIds) {
      if (!usersIds.length) {
        return;
      }

      try {
        this.areUsersLoaded = false;

        const res = await api.get(`${USERS_URL}?ids=${usersIds.join(",")}`);

        const fetchedUsers = res.data.items.reduce((acc, user) => {
          acc[user.sub] = {
            name: user.name,
            email: user.email,
          };
          return acc;
        }, {});

        this.users = { ...this.users, ...fetchedUsers };

        this.areUsersLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Retrieves user information based on the provided user ID.
     *
     * @param {string} userId - The ID of the user to retrieve information for.
     * @returns {Object|null} An object containing the user's name and email if the user is found,
     * or null if no userId is provided.
     *
     * If the user data is already available in the store, it returns the data immediately.
     * Otherwise, it fetches the user's data from the server and updates the store before returning the data.
     */
    async getUserInfo(userId) {
      if (!userId) {
        return null;
      }

      if (this.users[userId]) {
        return {
          name: this.users[userId]?.name,
          email: this.users[userId]?.email,
        };
      }

      // Just call the existing method with one ID only
      await this.fetchUsers([userId]);

      return {
        name: this.users[userId]?.name,
        email: this.users[userId]?.email,
      };
    },
  },
});
