import { reactive } from "vue";
import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
import { createAPI } from "@/api/index";
import { useUsersStore } from "@/store/users";
import { PRODUCT_STATE } from "@/constants";
import { formatDate } from "@/utils/formatDate";

const api = createAPI();

const PRODUCTS_URL = `${import.meta.env.VITE_PORTAL_API_URL}/products`;

export const PRODUCT_STATUS = {
  FILE_MISSING: {
    name: PRODUCT_STATE.FILE_MISSING,
    message: "No file uploaded yet",
    icon: "mdi-file-question-outline",
    iconColor: "#b09000",
  },
  PREFLIGHT: {
    name: PRODUCT_STATE.PREFLIGHT,
    message: "Layout preflight in progress",
    icon: "mdi-airplane",
    iconColor: "#1562af",
  },
  PROOFING_INITIAL: {
    name: PRODUCT_STATE.PROOFING_INITIAL,
    message: "Layout waiting for approval",
    icon: "mdi-creation-outline",
    iconColor: "#b09000",
  },
  PRINTMARKS_MISSING: {
    name: PRODUCT_STATE.PRINTMARKS_MISSING,
    message: "Printmarks need to be placed",
    icon: "mdi-artboard",
    iconColor: "#b09000",
  },
  PROCESSING: {
    name: PRODUCT_STATE.PROCESSING,
    message: "Processing layout changes",
    icon: "mdi-content-save-cog-outline",
    iconColor: "#1562af",
  },
  PROOF_MISSING: {
    name: PRODUCT_STATE.PROOF_MISSING,
    message: "Layout changes need approval",
    icon: "mdi-file-edit-outline",
    iconColor: "#b09000",
  },
  PROOF_INITIALIZED: {
    name: PRODUCT_STATE.PROOF_INITIALIZED,
    message: "Approval initialized",
    icon: "mdi-cloud-cog-outline",
    iconColor: "#1562af",
  },
  PROOFING_CHANGE: {
    name: PRODUCT_STATE.PROOFING_CHANGE,
    message: "Changes waiting for approval",
    icon: "mdi-file-sign",
    iconColor: "#b09000",
  },
  READY: {
    name: PRODUCT_STATE.READY,
    message: "Ready for orders",
    icon: "mdi-package-variant",
    iconColor: "#007e00",
  },
  DONE: {
    name: PRODUCT_STATE.DONE,
    message: "Ready for more orders",
    icon: "mdi-package-variant-closed-check",
    iconColor: "#007e00",
  },
  ERROR_PREFLIGHT: {
    name: PRODUCT_STATE.ERROR_PREFLIGHT,
    message: "Error during preflight",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
  ERROR_PROOFING_INITIAL: {
    name: PRODUCT_STATE.ERROR_PROOFING_INITIAL,
    message: "Error approving file",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
  ERROR_PROCESSING: {
    name: PRODUCT_STATE.ERROR_PROCESSING,
    message: "Error processing changes",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
  ERROR_PROOF_INITIALIZED: {
    name: PRODUCT_STATE.ERROR_PROOF_INITIALIZED,
    message: "Error initializing approval",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
  ERROR_PROOFING_CHANGE: {
    name: PRODUCT_STATE.ERROR_PROOFING_CHANGE,
    message: "Error approving changes",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
  FILE_PREPROCESSING: {
    name: PRODUCT_STATE.FILE_PREPROCESSING,
    message: "File is in preprocessing",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
  ERROR_UPLOADING: {
    name: PRODUCT_STATE.ERROR_UPLOADING,
    message: "Error uploading file",
    icon: "mdi-alert-rhombus",
    iconColor: "#b00020",
  },
};

export const useProductsStore = defineStore("products", {
  /**
   * State of the store containing:
   *
   * @property {Object[]} products - List of products
   * @property {string} currentSortField - Current sort field used in the table
   * @property {string} currentSortOrder - Current sort order used in the table
   * @property {boolean} productsLoaded - Whether the list of products has been loaded
   * @property {number} currentPage - Current page number
   * @property {number} currentPageSize - Current amount of items per page
   * @property {number} productsTotal - Total amount of products (will be taken from response)
   * @property {Object} selectedProduct - The selected product
   * @property {Object} useToast - A toast notification instance for displaying messages.
   * @property {Object} currentFilters - A reactive object representing the current filters
   * @property {UsersStore} useUsersStore - Users store
   */
  state: () => ({
    products: [],
    currentSortField: "created",
    currentSortOrder: "desc",
    currentPage: 1,
    currentPageSize: 10,
    productsTotal: 10,
    productsLoaded: false,
    selectedProduct: null,
    autoCodes: null,
    selectedOrderAutoCodes: null,
    useToast: useToast(),
    useUsersStore: useUsersStore(),
    currentFilters: reactive({
      id: {
        value: "",
        type: "input",
        label: "Product ID",
      },
      name: {
        value: "",
        type: "input",
        label: "Name",
      },
      brandownersNames: {
        value: [],
        type: "multiSelect",
        label: "Customer",
      },
      customerProductId: {
        value: "",
        type: "input",
        label: "Product Number",
      },
      ownerName: {
        value: "",
        type: "input",
        label: "Owner Name",
      },
      recipesNames: {
        value: [],
        type: "multiSelect",
        label: "Papertype",
      },
      createdBefore: {
        value: null,
        type: "dateInput",
        label: "Created before",
      },
      createdAfter: {
        value: null,
        type: "dateInput",
        label: "Created after",
      },
      state: {
        value: [],
        type: "multiSelect",
        label: "State",
      },
    }),
  }),

  getters: {
    /**
     * Returns the list of product IDs, the date created formatted and sorted by the current sort field and order.
     * @returns {string[]} The list of sorted product IDs.
     */
    filteredProductIds() {
      return this.products
        .map((product) => {
          if (product.created) {
            product.created = formatDate(product.created);
          }

          return product;
        })
        .sort((a, b) => {
          if (this.currentSortOrder === "asc") {
            return a[this.currentSortField].toLowerCase() >
              b[this.currentSortField].toLowerCase()
              ? 1
              : -1;
          } else {
            return a[this.currentSortField].toLowerCase() <
              b[this.currentSortField].toLowerCase()
              ? 1
              : -1;
          }
        })
        .map((product) => product.id);
    },

    /**
     * Returns the index of the selected product in the filtered list of products.
     * If there is no selected product, returns null.
     * @returns {number|null} The index of the selected product or null.
     */
    selectedProductIndex() {
      if (!this.selectedProduct) {
        return null;
      }

      return this.filteredProductIds.indexOf(this.selectedProduct.id);
    },
  },

  actions: {
    /**
     * Fetches the list of products from the server and updates the store.
     *
     * Also fetches needed users from the server and loads them into the users store.
     *
     * @async
     */
    async fetchProducts() {
      try {
        // We need `?view=1` param to get endpoint with 'brandowner_name' and `recipe_name`
        const paramsArr = ["view=1"];

        if (this.currentSortOrder && this.currentSortField) {
          const sortOrder = this.currentSortOrder === "asc" ? "+" : "-";
          paramsArr.push(`sort=${sortOrder}${this.currentSortField}`);
        }

        paramsArr.push(`page=${this.currentPage}`);
        paramsArr.push(`size=${this.currentPageSize}`);

        const filtersParams = [];

        const filterId = this.currentFilters.id.value;
        const filterName = this.currentFilters.name.value;
        const filterBrandowners = this.currentFilters.brandownersNames.value;
        const filterCustomerProductId =
          this.currentFilters.customerProductId.value;
        const filterOwnerName = this.currentFilters.ownerName.value;
        const filterRecipesNames = this.currentFilters.recipesNames.value;
        const filterCreatedBefore = this.currentFilters.createdBefore.value;
        const filterCreatedAfter = this.currentFilters.createdAfter.value;
        const filterStates = this.currentFilters.state.value;

        if (filterId) {
          const parsedFilterId = Number(filterId) || -1;
          filtersParams.push(`id=${parsedFilterId}`);
        }
        if (filterName) {
          filtersParams.push(`name~=${filterName}`);
        }
        if (filterBrandowners.length) {
          filtersParams.push(`brandowner_id=${filterBrandowners.join("|")}`);
        }
        if (filterCustomerProductId) {
          filtersParams.push(`product_number~=${filterCustomerProductId}`);
        }
        if (filterOwnerName) {
          filtersParams.push(`owner_name~=${filterOwnerName}`);
        }
        if (filterRecipesNames.length) {
          filtersParams.push(`recipe_id=${filterRecipesNames.join("|")}`);
        }
        if (filterCreatedBefore) {
          filtersParams.push(
            `created<=${new Date(filterCreatedBefore).toISOString()}`
          );
        }
        if (filterCreatedAfter) {
          filtersParams.push(
            `created>=${new Date(filterCreatedAfter).toISOString()}`
          );
        }
        if (filterStates.length) {
          filtersParams.push(`state=${filterStates.join("|")}`);
        }

        filtersParams.push("disabled=false");

        if (filtersParams.length) {
          paramsArr.push(`filter=${filtersParams.join(",")}`);
        }

        this.productsLoaded = false;

        const res = await api.get(`${PRODUCTS_URL}?${paramsArr.join("&")}`);

        // We need a set because we need to get rid of duplicate user IDs
        const userIdsSet = new Set();

        this.products = res.data.items.map((product) => {
          userIdsSet.add(product.owner_uuid);

          // We also add approver ID if it present for the product
          if (product.approval_user) {
            userIdsSet.add(product.approval_user);
          }

          return {
            ...product,
            state: PRODUCT_STATUS[product.state]?.name,
            stateObj: PRODUCT_STATUS[product.state],
          };
        });
        this.productsTotal = res.data.total;
        this.useUsersStore.fetchUsers([...userIdsSet]);

        this.productsLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Updates the state of a product by its ID.
     *
     * This function searches for a product in the store's list of products
     * using the provided productId. Once the target product is found,
     * it updates the product's state and state object to the new state specified.
     *
     * @param {string} productId - The identifier of the product to be updated.
     * @param {string} newState - The new state to assign to the product.
     */
    updateProductStateById(productId, newState) {
      const targetProduct = this.products.find(
        (product) => product.id === productId
      );

      targetProduct.state = PRODUCT_STATUS[newState]?.name;

      targetProduct.stateObj = PRODUCT_STATUS[newState];
    },

    /**
     * Asynchronously selects a product by its ID and updates the store with its data.
     *
     * This function sends a GET request to the server to retrieve detailed product information,
     * including the 'brandowner_name' and 'recipe_name', by using the `?view=1` query parameter.
     * After fetching the product data, it manually updates the product state if necessary and
     * retrieves additional user information for the product owner and approver. The selected
     * product's data, including its state, state object, owner object, and approver object,
     * is then stored in `this.selectedProduct`.
     *
     * If an error occurs during the fetch operation and there is no page reload flag in session
     * storage, an error toast message is displayed.
     *
     * @async
     * @param {string} productId - The ID of the product to be selected.
     * @throws {Error} - If the product data cannot be fetched.
     */
    async selectProduct(productId) {
      try {
        // We need `?view=1` param to get endpoint with 'brandowner_name' and `recipe_name`
        const res = await api.get(`${PRODUCTS_URL}/${productId}?view=1`);

        const ownerData = await this.useUsersStore.getUserInfo(
          res.data.item.owner_uuid
        );

        // Here we get either needed user object or null from the method
        const approverData = await this.useUsersStore.getUserInfo(
          res.data.item.approval_user
        );

        this.selectedProduct = {
          ...res.data.item,
          state: PRODUCT_STATUS[res.data.item.state]?.name,
          stateObj: PRODUCT_STATUS[res.data.item.state],
          ownerObj: ownerData,
          approverObj: approverData, // either object or null
        };
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Updates the state and state object of the selected product.
     *
     * This function changes the state and state object of the currently selected
     * product to the new state specified. It uses the PRODUCT_STATUS constant
     * to map the new state to its corresponding name and object.
     *
     * @param {string} newState - The new state to assign to the selected product.
     */
    updateSelectedProductState(newState) {
      this.selectedProduct.state = PRODUCT_STATUS[newState]?.name;
      this.selectedProduct.stateObj = PRODUCT_STATUS[newState];
    },

    /**
     * Creates a new product on the server and returns its ID.
     *
     * This function sends a POST request to create a new product with the given
     * data. If the creation is successful, it displays a success toast message
     * and returns the ID of the newly created product.
     *
     * If the creation fails and there is no page reload flag in session storage,
     * it displays an error toast message indicating the failure to create the product.
     *
     * @async
     * @param {Object} item - The product data to create.
     * @returns {Promise<number | false>} - A promise that resolves to the ID of
     * the newly created product (or false if the creation fails).
     */
    async createProduct(item) {
      try {
        let productId = null;

        const res = await api.post(PRODUCTS_URL, item);

        this.useToast.success("Product created", {
          timeout: 1000,
        });

        productId = res.data.item.id;

        return productId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return false;
      }
    },

    /**
     * Updates a product on the server and returns its ID.
     *
     * This function sends a PUT request to update the product identified by the
     * item's ID with the given data. If the update is successful, it displays a
     * success toast message and returns the ID of the updated product.
     *
     * If the update fails and there is no page reload flag in session storage,
     * it displays an error toast message indicating the failure to update the product.
     *
     * @async
     * @param {Object} item - The product data to update.
     * @param {Object} data - The data to update the product with.
     * @returns {Promise<number | undefined>} - A promise that resolves to the ID of
     * the updated product (or undefined if the update fails).
     */
    async updateProduct(item, data) {
      try {
        let productId = null;

        const res = await api.put(`${PRODUCTS_URL}/${item.id}`, data);

        this.useToast.success("Product updated", {
          timeout: 1000,
        });

        productId = res.data.item.id;

        return productId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Creates a copy of the given product on the server and returns the new product's ID.
     *
     * Sends a POST request to copy the product identified by `item.id`, using the provided `data`.
     * If the operation succeeds, a success toast is displayed and the newly created product's ID is returned.
     *
     * In case of an error, and if the `pageReload` flag is not set in session storage,
     * an error toast is shown with the relevant message.
     *
     * @async
     * @param {Object} item - The original product object containing the `id` to be copied.
     * @param {Object} data - The payload to send in the copy request.
     * @returns {Promise<number | undefined>} - The ID of the copied product on success, or `undefined` on failure.
     */

    async copyProduct(item, data) {
      try {
        let productId = null;

        const res = await api.post(`${PRODUCTS_URL}/${item.id}/copy`, data);

        this.useToast.success("Product copied", {
          timeout: 1000,
        });

        productId = res.data.item.id;

        return productId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Deletes a product on the server and returns its ID.
     *
     * This function sends a DELETE request to delete the product identified by the
     * item's ID. If the deletion is successful, it displays a success toast message
     * and returns the ID of the deleted product.
     *
     * If the deletion fails and there is no page reload flag in session storage,
     * it displays an error toast message indicating the failure to delete the product.
     *
     * @async
     * @param {Object} item - The product data to delete.
     * @returns {Promise<number | undefined>} - A promise that resolves to the ID of
     * the deleted product (or undefined if the deletion fails).
     */
    async deleteProduct(item) {
      try {
        const res = await api.delete(`${PRODUCTS_URL}/${item.id}`);

        if (res.data.status === "success") {
          this.useToast.success("Product deleted", {
            timeout: 1000,
          });
        }

        return item.id;
      } catch (error) {
        throw error;
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Initializes the proofing process for a product for a given product id.
     *
     * This function sends a POST request to the server to initialize the
     * proofing process for the product with the given id. Upon successful
     * initialization, it sets the product state to PROOF_INITIALIZED and
     * displays a success toast message.
     *
     * If the initialization fails, it displays an error toast message unless
     * the page reload flag is set in session storage.
     *
     * @async
     * @param {Object} item - The product data to initialize proofing for.
     * @param {number} item.id - The id of the product to initialize proofing for.
     */
    async initializeProductProofing(item) {
      try {
        await api.post(`${PRODUCTS_URL}/${item.id}/proof`);

        this.updateSelectedProductState(PRODUCT_STATE.PROOF_INITIALIZED);

        this.useToast.success("Proofing initialized!", {
          timeout: 2000,
        });
      } catch {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },
    async getAutoCodes(productId) {
      try {
        const response = await api.get(`${PRODUCTS_URL}/${productId}/autocodes`);
        response.data.items.forEach((item) => {

          const incrementStep = Number(item.config.incrementStep);
          const lastPrinted = Number(item.config.lastIncrementPrinted);
          item.config.lastIncrementPrinted =
            lastPrinted > item.config.incrementStart
              ? lastPrinted + incrementStep
              : lastPrinted;
        });
        this.autoCodes = response.data.items;
      } catch (error) {
        console.log(error);
      }
    },

    async getSelectedOrderAutoCodes(productId, orderId) {
      try {
        const response = await api.get(`${PRODUCTS_URL}/${productId}/orders/${orderId}/vdp`);

          const transformedArray = Object.values(
            response.data.items.reduce((accumulator, item) => {
              const { product_vdp_id, key_name, key_value } = item;
              if (!accumulator[product_vdp_id]) {
                accumulator[product_vdp_id] = {
                  id: parseInt(product_vdp_id, 10),
                  variableName: null,
                  incrementStart: null,
                  incrementStep: null,
                };
              }

              // Assign incrementStart or incrementStep based on key_name
              if (key_name === "incrementStart") {
                accumulator[product_vdp_id].incrementStart = parseInt(key_value, 10);
              } else if (key_name === "incrementStep") {
                accumulator[product_vdp_id].incrementStep = parseInt(key_value, 10);
              } else if (key_name === "variableName") {
                accumulator[product_vdp_id].variableName = key_value.replace("AUTO_CODE_INCREMENT_", "");;
              }

              return accumulator;
            }, {})
          );

          console.log(transformedArray);

       this.selectedOrderAutoCodes = transformedArray;
      } catch (error) {
        console.log(error);
      }
    },

    /**
     *  Updates a specific product in the `products` array by matching ID.
     *  If a product with such an ID is found, it is replaced by a new product object.
     *  Used for point-by-point updating of a product without changing the order of elements.
     *
     * @param {Object} product - The product with updated data.
     */

    updateProductData(product) {
      const index = this.products.findIndex((item) => item.id === product.id);

      if (index !== -1) {
        this.products.splice(index, 1, {
          ...product,
          state: PRODUCT_STATUS[product.state]?.name,
          stateObj: PRODUCT_STATUS[product.state],
        });
      }
    },

    /**
     * Resets the product state to FILE_MISSING.
     *
     * This function sends a POST request to the server to reset the product
     * state to FILE_MISSING. Upon successful reset, it updates the selected
     * product state and displays a success toast message.
     *
     * If the reset fails, it displays an error toast message unless the page
     * reload flag is set in session storage.
     *
     * @async
     * @param {Object} item - The product data to reset.
     * @param {number} item.id - The id of the product to reset.
     */
    async resetProduct(item) {
      try {
        await api.post(`${PRODUCTS_URL}/${item.id}/reset`);

        this.updateSelectedProductState(PRODUCT_STATE.FILE_MISSING);

        this.useToast.success("Product reset!", {
          timeout: 2000,
        });
      } catch {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },
  },
});
