import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
import { createAPI } from "@/api/index";
import { reactive } from "vue";

const api = createAPI();

const RECIPES_URL = `${import.meta.env.VITE_PORTAL_API_URL}/recipes`;

export const useRecipesStore = defineStore("recipes", {
  /**
   * The state of the recipes store.
   *
   * @property {Object[]} recipes - List of recipes
   * @property {Object[]} recipesNames - an array of recipe objects (id and name)
   * @property {boolean} recipesLoaded - Whether the list of recipes has been loaded
   * @property {boolean} recipesNamesLoaded - a boolean indicating if the recipes names array is loaded
   * @property {Object} useToast - A toast notification instance for displaying messages.
   * @property {string} currentSortField - The current sort field
   * @property {string} currentSortOrder - The current sort order
   * @property {number} currentPage - The current page
   * @property {number} currentPageSize - The current page size
   * @property {number} recipesTotal - The total number of recipes
   * @property {Object} currentFilters - The current filters
   */
  state: () => ({
    recipes: [],
    recipesNames: [],
    recipesLoaded: false,
    recipesNamesLoaded: false,
    currentSortField: "created",
    currentSortOrder: "desc",
    currentPage: 1,
    currentPageSize: 10,
    recipesTotal: 10,
    useToast: useToast(),
    currentFilters: reactive({
      id: {
        value: "",
        type: "input",
        label: "ID"
      },
      name:
        { value: "",
          type: "input",
          label: "Name"
        },
      createdBefore: {
        value: null,
        type: "dateInput",
        label: "Created before",
      },
      createdAfter: {
        value: null,
        type: "dateInput",
        label: "Created after"
      },
      disabled: {
        value: [],
        type: "multiSelect",
        label: "Status"
      },
    }),
  }),

  actions: {
    /**
     * Fetches the list of recipes from the API and updates the state.
     *
     * If the request fails, displays an error toast message.
     *
     * @async
     * @returns {Promise<number|null>} - A promise that resolves to the total amount of recipes (or null)
     */
    async fetchRecipes() {
      let totalRecipes = null;

      try {
        const resultArr = [];
        if (this.currentSortOrder && this.currentSortField) {
          const sortOrder = this.currentSortOrder === "asc" ? "+" : "-";
          resultArr.push(`sort=${sortOrder}${this.currentSortField}`);
        }
        resultArr.push(`page=${this.currentPage}`);
        resultArr.push(`size=${this.currentPageSize}`);
        const filtersParams = [];
        const filterId = this.currentFilters.id.value;
        const filterName = this.currentFilters.name.value;
        const filterCreatedBefore = this.currentFilters.createdBefore.value;
        const filterCreatedAfter = this.currentFilters.createdAfter.value;
        const filterDisabledValues = this.currentFilters.disabled.value;
        if (filterId) {
          const parsedFilterId = Number(filterId) || -1;
          filtersParams.push(`id=${parsedFilterId}`);
        }
        if (filterName) {
          filtersParams.push(`name~=${filterName}`);
        }
        if (filterCreatedBefore) {
          filtersParams.push(
            `created<=${new Date(filterCreatedBefore).toISOString()}`
          );
        }
        if (filterCreatedAfter) {
          filtersParams.push(
            `created>=${new Date(filterCreatedAfter).toISOString()}`
          );
        }
        if (filterDisabledValues.length) {
          filtersParams.push(`disabled=${filterDisabledValues.join("|")}`);
        }
        if (filtersParams.length) {
          resultArr.push(`filter=${filtersParams.join(",")}`);
        }
        let targetUrl = RECIPES_URL;
        if (resultArr.length) {
          targetUrl += `?${resultArr.join("&")}`;
        }
        this.recipesLoaded = false;
        const res = await api.get(targetUrl);
        this.recipes = res.data.items;
        totalRecipes = res.data.total;
        this.recipesTotal = totalRecipes;
        this.recipesLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return totalRecipes;
    },

    /**
     * Fetches the list of recipes names from the server and updates the store's state.
     *
     * Sends a GET request to the RECIPES_URL to retrieve recipe data.
     * Updates the `recipes` array with the fetched data and sets the
     * `recipesLoaded` flag to true upon successful fetching.
     *
     * In case of an error, displays a toast notification unless the
     * page reload flag is set in session storage.
     *
     * @async
     */
    async fetchRecipesNames() {
      try {
        this.recipesNamesLoaded = false;

        const res = await api.get(`${RECIPES_URL}?size=-1`);

        // TODO: replace fetching all fields on fetching only `id` and `name` from server
        this.recipesNames = res.data.items.map((recipe) => {
          return { value: recipe.id, text: recipe.name };
        });

        this.recipesNamesLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Updates the status of a recipe by enabling/disabling it.
     *
     * Takes a `item` object and a boolean `isDisabled` as parameters.
     * Sends a PUT request to the server to update the recipe's status.
     * If the update is successful, it displays a toast notification and
     * returns the ID of the updated recipe.
     * If the update fails, it displays an error toast notification unless
     * the page reload flag is set in session storage.
     *
     * @async
     * @param {Object} item - The recipe data to update.
     * @param {boolean} isDisabled - The status to set for the recipe.
     * @returns {Promise<number | null>} - A promise that resolves to the ID of the
     * updated recipe (or null if the update fails).
     */
    async updateRecipeStatus(item, isDisabled) {
      try {
        let recipeId = null;

        const res = await api.put(`${RECIPES_URL}/${item.id}`, {
          disabled: isDisabled,
        });

        this.useToast.success("Recipe status updated", {
          timeout: 1000,
        });

        recipeId = res.data.item.id;

        return recipeId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     *  Updates a specific recipe in the `recipes` array by matching ID.
     *  If a recipe with such an ID is found, it is replaced by a new recipe object.
     *  Used for point-by-point updating of a recipe without changing the order of elements.
     *
     * @param {Object} recipe - The recipe with updated data.
     */
    updateRecipeData(recipe) {
      const index = this.recipes.findIndex((item) => item.id === recipe.id);

      if (index !== -1) {
        this.recipes.splice(index, 1, recipe);
      }
    },
  },
});
