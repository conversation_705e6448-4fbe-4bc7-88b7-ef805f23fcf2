import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
import { createAPI } from "@/api/index";

const api = createAPI();

const PLANTS_URL = `${import.meta.env.VITE_PORTAL_API_URL}/plants`;
const DPUS_URL = `${import.meta.env.VITE_PORTAL_API_URL}/dpus`;
const RECIPES_URL = `${import.meta.env.VITE_PORTAL_API_URL}/recipes`;

export const usePlantsStore = defineStore("plants", {
  /**
   * The state of the plants store.
   *
   * @property {Object[]} plantsNames - an array of plant objects (id and name)
   * @property {Object[]} dpusNames - an array of dpus objects (id and name)
   * @property {boolean} plantsNamesLoaded - a boolean indicating if the plants array is loaded
   * @property {boolean} dpusNamesLoaded - a boolean indicating if the dpus array is loaded
   * @property {Object} plantsDpusNamesMap - a map of plant Dpus names, where the key is the ID of the plant
   *   and the value is an array of Dpu objects
   * @property {Object} plantDpusNamesLoaded - a map indicating if the Dpus names are loaded for a given plant,
   *   where the key is the ID of the plant and the value is a boolean
   * @property {Object} useToast - A toast notification instance for displaying messages.
   */
  state: () => ({
    plantsNames: [],
    dpusNames: [],
    plantsNamesLoaded: false,
    dpusNamesLoaded: false,
    plantsDpusNamesMap: {}, // key - id of the plant, value - array of plant Dpus names
    plantDpusNamesLoaded: {}, // key - id of the plant, value - boolean indicating if Dpus names are loaded
    dpusRecipesNamesMap: {}, // key - id of the dpu, value - array of dpu Recipes names
    dpusRecipesNamesLoaded: {}, // key - id of the dpu, value - boolean indicating if Recipes names are loaded
    useToast: useToast(),
  }),

  getters: {
    /**
     * Returns the cached Dpus names for the given plant ID, or null if the Dpus names
     * have not been loaded for that plant.
     *
     * @param {number} plantId - the ID of the plant
     * @returns {Object[]} - the cached Dpus names for the given plant, or null
     */
    getPlantDpusNames: (state) => (plantId) => {
      // Return the cached Dpus if they exist
      return state.plantsDpusNamesMap[plantId] || null;
    },

    /**
     * Returns the cached Recipes names for the given dpu ID, or null if the Recipes names
     * have not been loaded for that dpu.
     *
     * @param {number} dpuId - the ID of the dpu
     * @returns {Object[]} - the cached Recipes names for the given dpu, or null
     */
    getDpuRecipesNames: (state) => (dpuId) => {
      // Return the cached Dpus if they exist
      return state.dpusRecipesNamesMap[dpuId] || null;
    },
  },

  actions: {
    /**
     * Fetches the list of plants names from the API and updates the state.
     *
     * If the request fails, displays an error toast message.
     *
     * @async
     */
    async fetchPlantsNames() {
      try {
        const res = await api.get(`${PLANTS_URL}?size=-1`);

        // TODO: replace fetching all fields on fetching only `id` and `name` from server
        this.plantsNames = res.data.items.map((plant) => {
          return { value: plant.id, text: plant.name };
        });

        this.plantsNamesLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Loads the Dpus names for a specified plant. If the Dpus names for the plant are already loaded,
     * returns the cached Dpus names. Otherwise, fetches the Dpus names from the API.
     *
     * @async
     * @param {number} plantId - The ID of the plant for which to load Dpus names.
     * @returns {Promise<Object[]>} - A promise that resolves to the array of Dpus names for the plant.
     */
    async loadPlantDpusNames(plantId) {
      // Check if the Dpus names for this plant are already loaded
      if (this.plantDpusNamesLoaded[plantId]) {
        return this.plantsDpusNamesMap[plantId];
      } else {
        return await this.fetchPlantDpusNames(plantId);
      }
    },

    /**
     * Fetches the Dpus names for a specified plant from the API and caches the
     * response. If the request fails, displays an error toast message.
     *
     * @async
     * @param {number} plantId - The ID of the plant for which to fetch Dpus names.
     * @returns {Promise<Object[]>} - A promise that resolves to the array of Dpus names for the plant.
     */
    async fetchPlantDpusNames(plantId) {
      try {
        const res = await api.get(`${PLANTS_URL}/${plantId}/dpus?size=-1`);

        // TODO: replace fetching all fields on fetching only `id` and `name` from server
        this.plantsDpusNamesMap[plantId] = res.data.items.map((plantDpu) => {
          return { value: plantDpu.id, text: plantDpu.name };
        });

        this.plantDpusNamesLoaded[plantId] = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return this.plantsDpusNamesMap[plantId];
    },

    /**
     * Loads the Recipes names for a specified dpu. If the Recipes names for the dpu are already loaded,
     * returns the cached Recipes names. Otherwise, fetches the Recipes names from the API.
     *
     * @async
     * @param {number} dpuId - The ID of the dpu for which to load Recipes names.
     * @returns {Promise<Object[]>} - A promise that resolves to the array of Recipes names for the dpu.
     */
    async loadDpuRecipesNames(dpuId) {
      // Check if the Recipes names for this dpu are already loaded
      if (this.dpusRecipesNamesLoaded[dpuId]) {
        return this.dpusRecipesNamesMap[dpuId];
      } else {
        return await this.fetchDpuRecipesNames(dpuId);
      }
    },

    /**
     * Fetches the Recipes names for a specified dpu from the API and caches the
     * response. If the request fails, displays an error toast message.
     *
     * @async
     * @param {number} dpuId - The ID of the dpu for which to fetch Recipes names.
     * @returns {Promise<Object[]>} - A promise that resolves to the array of Recipes names for the dpu.
     */
    async fetchDpuRecipesNames(dpuId) {
      try {
        const res = await api.get(
          `${RECIPES_URL}?filter=dpu_id=${dpuId}&size=0&sort=name`
        );

        // TODO: replace fetching all fields on fetching only `id` and `name` from server
        this.dpusRecipesNamesMap[dpuId] = res.data.items.map((dpuRecipe) => {
          return { value: dpuRecipe.id, text: dpuRecipe.name };
        });

        this.dpusRecipesNamesLoaded[dpuId] = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return this.dpusRecipesNamesMap[dpuId];
    },

    /**
     * Fetches all dpus names from the API and caches the response.
     *
     * If the request fails, displays an error toast message.
     *
     * @async
     */
    async fetchAllDpusNames() {
      try {
        this.dpusNamesLoaded = false;

        const res = await api.get(`${DPUS_URL}?size=-1`);

        // TODO: replace fetching all fields on fetching only `id` and `name` from server
        this.dpusNames = res.data.items.map((dpu) => {
          return { value: dpu.id, text: dpu.name };
        });

        this.dpusNamesLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },
  },
});
