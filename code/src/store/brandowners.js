import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
import { createAPI } from "@/api/index";
import { reactive } from "vue";

const api = createAPI();

// @TODO: switch to using valid endpoint for brandowners once it will be implemented
const BRANDOWNERS_URL = `${import.meta.env.VITE_PORTAL_API_URL}/brandowners`;

export const useBrandownersStore = defineStore("brandowners", {
  /**
   * State of the store containing:
   *
   * @property {Object[]} brandowners - List of brandowners
   * @property {Object[]} brandownersNames - List of all brandowners names
   * @property {boolean} brandownersLoaded - Whether the list of brandowners has been loaded
   * @property {boolean} brandownersNamesLoaded - Whether the list of brandowners names has been loaded
   * @property {Object} selectedBrandowner - The selected brandowner
   * @property {number} currentPage - Current page number
   * @property {number} currentPageSize - Number of items per page
   * @property {number} brandownersTotal - Total count of brandowners
   * @property {string} currentSortField - The current sort field
   * @property {string} currentSortOrder - The current sort order
   * @property {Object} useToast - A toast notification instance for displaying messages.
   * @property {Object[]} mockedData - Mocked data for testing purposes
   */
  state: () => ({
    brandowners: [],
    brandownersNames: [],
    brandownersLoaded: false,
    brandownersNamesLoaded: false,
    selectedBrandowner: null,
    currentPage: 1,
    currentPageSize: 10,
    brandownersTotal: 10,
    currentSortField: "created",
    currentSortOrder: "desc",
    useToast: useToast(),
    currentFilters: reactive({
      id: {
        value: "",
        type: "input",
        label: "ID",
      },
      name: {
        value: "",
        type: "input",
        label: "Name",
      },
      description: {
        value: "",
        type: "input",
        label: "Description",
      },
      address: {
        value: "",
        type: "input",
        label: "Local Address",
      },
      contact: {
        value: "",
        type: "input",
        label: "Mail Contact",
      },
      taxId: {
        value: "",
        type: "input",
        label: "Customer ID",
      },
      archived: {
        value: [],
        type: "multiSelect",
        label: "Activation status",
      },
    }),
    mockedData: [
      {
        id: 1,
        name: "Brandowner 1",
        description: "Generic description",
        address: "Address",
        contact: "Contact",
        taxid: "234",
      },
    ],
  }),

  actions: {
    /**
     * Fetches the list of brandowners from the API and updates the state.
     *
     * If the request fails, displays an error toast message.
     *
     * @async
     * @returns {Promise<number|null>} - A promise that resolves to the total amount of brandowners (or null)
     */
    async fetchBrandowners() {
      let totalBrandowners = null;

      try {
        const resultArr = [];
        if (this.currentSortOrder && this.currentSortField) {
          const sortOrder = this.currentSortOrder === "asc" ? "+" : "-";
          resultArr.push(`sort=${sortOrder}${this.currentSortField}`);
        }
        resultArr.push(`page=${this.currentPage}`);
        resultArr.push(`size=${this.currentPageSize}`);
        const filtersParams = [];
        const filterId = this.currentFilters.id.value;
        const filterName = this.currentFilters.name.value;
        const filterDescription = this.currentFilters.description.value;
        const filterAddress = this.currentFilters.address.value;
        const filterContact = this.currentFilters.contact.value;
        const filterTaxId = this.currentFilters.taxId.value;
        const filterArchivedValues = this.currentFilters.archived.value;

        if (filterId) {
          const parsedFilterId = Number(filterId) || -1;
          filtersParams.push(`id=${parsedFilterId}`);
        }
        if (filterName) {
          filtersParams.push(`name~=${filterName}`);
        }
        if (filterDescription) {
          filtersParams.push(`description~=${filterDescription}`);
        }
        if (filterAddress) {
          filtersParams.push(`address~=${filterAddress}`);
        }
        if (filterContact) {
          filtersParams.push(`contact~=${filterContact}`);
        }
        if (filterTaxId) {
          filtersParams.push(`taxid~=${filterTaxId}`);
        }
        if (filterArchivedValues.length) {
          filtersParams.push(`archived=${filterArchivedValues.join("|")}`);
        }

        if (filtersParams.length) {
          resultArr.push(`filter=${filtersParams.join(",")}`);
        }
        let targetUrl = BRANDOWNERS_URL;
        if (resultArr.length) {
          targetUrl += `?${resultArr.join("&")}`;
        }
        this.brandownersLoaded = false;
        const res = await api.get(targetUrl);
        this.brandowners = res.data.items;
        totalBrandowners = res.data.total;
        this.brandownersTotal = totalBrandowners;
        this.brandownersLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return totalBrandowners;
    },

    /**
     * Fetches all active brandowners names from the API and updates the state.
     *
     * If the request fails, displays an error toast message.
     *
     * @async
     */
    async fetchAllActiveBrandownersNames() {
      try {
        this.brandownersNamesLoaded = false;

        const res = await api.get(`${BRANDOWNERS_URL}?size=-1`);

        // TODO: replace fetching all fields on fetching only `id` and `name` from server
        this.brandownersNames = res.data.items
          .filter((brandowner) => !brandowner.archived)
          .map((brandowner) => {
            return { value: brandowner.id, text: brandowner.name };
          });

        this.brandownersNamesLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Selects a brandowner by its ID and sets it as the selected brandowner.
     * Fetches the latest list of brandowners before attempting to find the specified brandowner.
     * Logs an error message if fetching fails.
     *
     * @async
     * @param {number} id - The ID of the brandowner to select.
     */
    async selectBrandowner(id) {
      try {
        await this.fetchBrandowners();

        this.selectedBrandowner = this.brandowners.find(
          (item) => item.id === id
        );
      } catch (error) {
        console.error("Cannot fetch selected customer");
      }
    },

    /**
     * Creates a new brandowner and returns its ID.
     *
     * This function uses the portal's API to create a new brandowner with the
     * given data. If the creation is successful, it returns the ID of the
     * newly created brandowner. If the creation fails, it returns false.
     *
     * @async
     * @param {Object} item - The brandowner data to create.
     * @returns {Promise<number | false>} - A promise that resolves to the ID of
     * the newly created brandowner (or false if the creation fails).
     */
    async createBrandowner(item) {
      try {
        let brandownerId = null;

        const res = await api.post(BRANDOWNERS_URL, item);

        this.useToast.success("Customer created", {
          timeout: 1000,
        });

        brandownerId = res.data.item.id;

        return brandownerId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return false;
      }
    },

    /**
     * Updates a brandowner using the provided item data and returns its ID.
     *
     * This function sends a PUT request to update the brandowner with the specified ID
     * on the server. Upon successful update, it displays a success toast message and
     * returns the ID of the updated brandowner.
     *
     * If the update fails, it displays an error toast message unless the page reload flag
     * is set in session storage.
     *
     * @async
     * @param {Object} item - The brandowner data to update.
     * @param {Object} data - The data to update the brandowner with.
     * @returns {Promise<number | undefined>} - A promise that resolves to the ID of the
     * updated brandowner (or undefined if the update fails).
     */
    async updateBrandowner(item, data) {
      try {
        let brandownerId = null;

        const res = await api.put(`${BRANDOWNERS_URL}/${item.id}`, data);

        this.useToast.success("Customer updated", {
          timeout: 1000,
        });

        brandownerId = res.data.item.id;

        return brandownerId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Deletes a brandowner using the provided item data and returns its ID.
     *
     * This function sends a DELETE request to remove the brandowner identified by the
     * item's ID from the server. Upon successful deletion, it displays a success toast
     * message and returns the ID of the deleted brandowner.
     *
     * If the deletion fails, it displays an error toast message unless the page reload
     * flag is set in session storage.
     *
     * @async
     * @param {Object} item - The brandowner data containing the ID of the brandowner to delete.
     * @returns {Promise<number | undefined>} - A promise that resolves to the ID of the deleted
     * brandowner (or undefined if the deletion fails).
     */
    async deleteBrandowner(item) {
      try {
        const res = await api.delete(`${BRANDOWNERS_URL}/${item.id}`);

        if (res.data.status === "success") {
          this.useToast.success("Customer deleted", {
            timeout: 1000,
          });
        }

        return item.id;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     *  Updates a specific brandowner in the `brandowners` array by matching ID.
     *  If a brandowner with such an ID is found, it is replaced by a new brandowner object.
     *  Used for point-by-point updating of a recipe without changing the order of elements.
     *
     * @param {Object} brandowner - The brandowner with updated data.
     */
    updateBrandownerData(brandowner) {
      const index = this.brandowners.findIndex((item) => item.id === brandowner.id);

      if (index !== -1) {
        this.brandowners.splice(index, 1, brandowner);
      }
    },
  },
});
