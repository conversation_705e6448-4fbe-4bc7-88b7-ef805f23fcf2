import { defineStore } from "pinia";
import axios from "axios";
import { useToast } from "vue-toastification";
import { createAPI } from "@/api/index";
import useOriginalDimensions from "@/composables/useOriginalDimensions";
import { PRODUCT_STATE } from "@/constants";
import { useProductsStore } from "./products";

const api = createAPI();

const FILES_URL = `${import.meta.env.VITE_PORTAL_API_URL}/file-urls/`;

const originalDimensions = useOriginalDimensions();

export const useFilesStore = defineStore("files", {
  /**
   * Represents the state of the files store.
   *
   * @property {Array} files - An array to store file information.
   * @property {Array} uploadedFiles - An array to track the status of uploaded files.
   * @property {Object} useToast - A toast notification instance for displaying messages.
   */
  state: () => ({
    files: [],
    uploadedFiles: [],
    useToast: useToast(),
  }),

  actions: {
    /**
     * Retrieves the upload URL for a given product ID and file name.
     *
     * @async
     * @param {string} productId - The ID of the product.
     * @param {string} fileName - The name of the file to be uploaded.
     * @param {Object} [params] - An optional object to be merged with the file information.
     * @returns {Promise<string>} A promise resolving to the URL where the file can be uploaded.
     * @throws Will throw an error if the request for the URL fails.
     */
    async getUploadURL(productId, fileName, params) {
      try {
        const files = [{ name: fileName, method: "PUT" }];

        const combinedFiles = files.map((file) => ({
          ...file,
          ...(params ? params : {}),
        }));

        const data = JSON.stringify({
          files: combinedFiles,
        });

        const response = await api({
          url: `${FILES_URL}${productId}`,
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          data,
        });

        return response.data.items[0].url;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        throw error;
      }
    },

    /**
     * Uploads a file to S3 using the provided URL.
     *
     * @param {File} file - The file to be uploaded.
     * @param {number} index - The index of the file in the `uploadedFiles` array.
     * @param {string} URL - The URL where the file should be uploaded.
     * @returns {Promise} A promise resolving to `undefined` if the file is successfully uploaded, or rejecting with an error if the request fails.
     */
    uploadFileToS3(file, productId, index, URL) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.open("PUT", URL, true);
        xhr.setRequestHeader("Content-Type", file.type);

        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const percentComplete = (event.loaded / event.total) * 100;

            const targetFile = this.uploadedFiles[index];

            if (targetFile) {
              targetFile.progress = percentComplete;
            }
          }
        });
        // notice that the event handler is on xhr and not xhr.upload
        xhr.addEventListener("readystatechange", function (e) {
          if (this.readyState === 4) {
            const productStore = useProductsStore();
            productStore.updateProductStateById(
              productId,
              PRODUCT_STATE.FILE_PREPROCESSING
            );
          }
        });

        xhr.addEventListener("load", () => {
          if (xhr.status === 200) {
            resolve();
          } else {
            //set Error state
            const productStore = useProductsStore();
            productStore.updateProductStateById(
              productId,
              PRODUCT_STATE.ERROR_UPLOADING
            );

            reject(
              new Error(
                `Failed to upload file ${file.name}. Status: ${xhr.status}`
              )
            );
          }
        });

        xhr.addEventListener("error", () => {
          const productStore = useProductsStore();
          productStore.updateProductStateById(
            productId,
            PRODUCT_STATE.ERROR_UPLOADING
          );
          reject(
            new Error(`An error occurred while uploading file ${file.name}.`)
          );
        });

        xhr.send(file);
      });
    },

    /**
     * Uploads multiple files to S3 storage.
     *
     * @async
     * @param {Array<File>} files - An array of File objects to be uploaded.
     * @param {string} productId - The ID of the product associated with the files.
     * @param {string} fileType - The type of file being uploaded (e.g. 'layout', 'overlay').
     * @param {Object} params - An object of additional parameters to be sent with the request.
     * @returns {Promise<boolean>} A promise that resolves to `true` if all files were uploaded
     * successfully, `false` otherwise.
     */
    async uploadFiles(files, productId, fileType, params) {
      files.forEach((file) => {
        this.uploadedFiles.push({ fileName: file.name, file, progress: 0 });
      });

      try {
        await Promise.all(
          files.map(async (file, index) => {
            const url = await this.getUploadURL(productId, fileType, params);

            this.uploadFileToS3(file, productId, index, url);
          })
        );

        this.useToast.success("All files uploaded successfully!", {
          timeout: 1000,
        });

        return true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return false;
      }
    },

    /**
     * Retrieves a file from S3 storage for a given product ID and file name.
     *
     * This method sends a POST request to obtain the URL necessary for downloading
     * a specific file associated with a product. It constructs a request with the
     * provided product ID, file name, and optional parameters. If the request is
     * successful, it fetches the file from the obtained URL as a blob and returns
     * the file data. If any errors occur during the process, `false` is returned.
     *
     * @async
     * @param {string} productId - The ID of the product associated with the file.
     * @param {string} name - The name of the file to retrieve from S3 storage.
     * @param {Object} params - An object of additional parameters to be sent with the request.
     * @returns {Promise<Blob | boolean>} A promise that resolves to the file data if the request
     * is successful, `false` otherwise.
     */
    async getFileFromS3(productId, name, params) {
      try {
        const files = [{ name, method: "GET" }];

        const combinedFiles = files.map((file) => ({
          ...file,
          ...(params ? params : {}),
        }));

        const data = JSON.stringify({
          files: combinedFiles,
        });

        const response = await api({
          url: `${FILES_URL}${productId}`,
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          data,
        });

        if (response.data.items[0].status === "error") {
          throw new Error();
        }

        const res = await axios({
          method: "GET",
          url: response.data.items[0].url,
          responseType: "blob",
        });

        return res.data;
      } catch {
        return false;
      }
    },

    /**
     * Retrieves the thumbnail for a given product ID.
     *
     * This method sends a POST request to obtain the thumbnail URL for a specific
     * product, then fetches the thumbnail image as a blob. If the request is
     * successful, the dimensions of the original image are set, and the blob data
     * is returned. If an error occurs, an error message is shown via a toast
     * notification, and `false` is returned.
     *
     * @async
     * @param {string} productId - The ID of the product for which the thumbnail is retrieved.
     * @returns {Promise<Blob | boolean>} A promise that resolves to the blob data if the
     * request is successful, `false` otherwise.
     */
    async getThumbnail(productId) {
      try {
        const response = await api({
          url: `${FILES_URL}${productId}/thumb?size=480`,
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.data.item.status === "error") {
          throw new Error();
        }

        const res = await axios({
          method: "GET",
          url: response.data.item.url,
          responseType: "blob",
        });

        originalDimensions.setOriginalDimensions({
          width: res.headers["x-meta-sourcewidth"],
          height: res.headers["x-meta-sourceheight"],
        });

        return res.data;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return false;
      }
    },

    /**
     * Retrieves the full size thumbnail for a given product ID.
     *
     * This method sends a POST request to obtain the thumbnail URL for a specific
     * product, then fetches the thumbnail image as a blob. If the request is
     * successful, the blob data is returned.
     * If an error occurs, an error message is shown via a toast
     * notification, and `false` is returned.
     *
     * @async
     * @param {string} productId - The ID of the product for which the thumbnail is retrieved.
     * @returns {Promise<Blob | boolean>} A promise that resolves to the blob data if the
     * request is successful, `false` otherwise.
     */
    async getFullSizeThumbnail(productId) {
      try {
        const response = await api({
          url: `${FILES_URL}${productId}/thumb?size=0`,
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.data.item.status === "error") {
          throw new Error();
        }

        const res = await axios({
          method: "GET",
          url: response.data.item.url,
          responseType: "blob",
        });

        return res.data;
      } catch {
        return false;
      }
    },

    /**
     * Downloads a file from S3 storage as a blob.
     *
     * @async
     * @param {string} productId - The ID of the product related to the file.
     * @param {string} name - The name of the file to download.
     */
    async downloadFile(productId, name) {
      const url = await this.getFileFromS3(productId, name);

      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "file.pdf");
      link.setAttribute("target", "_blank");
      link.click();
    },

    /**
     * Deletes a file from S3 storage.
     *
     * @async
     * @param {string} productId - The ID of the product related to the file.
     */
    async deleteFile(productId) {
      alert("Deleting file for product", productId);
    },

    /**
     * Resets the list of uploaded files.
     */
    resetUploadedFiles() {
      this.uploadedFiles = [];
    },
  },
});
