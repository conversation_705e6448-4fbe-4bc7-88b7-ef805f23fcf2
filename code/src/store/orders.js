import { defineStore } from "pinia";
import { useToast } from "vue-toastification";
import { useHelper } from "@/composables/useHelper";
import { createAPI } from "@/api/index";
import { useUsersStore } from "@/store/users";
import { PRODUCT_STATE } from "@/constants";
import { reactive } from "vue";
import {useRecipesStore} from "@/store/recipes";

const api = createAPI();

const ROOT_URL = import.meta.env.VITE_PORTAL_API_URL;
const PRODUCTS_URL = `${import.meta.env.VITE_PORTAL_API_URL}/products`;

export const ORDER_STATUS = {
  READY: {
    name: "READY",
    message: "Ready to print",
    icon: "mdi-file-document-outline",
    iconColor: "#007e00",
  },
  PROCESSING: {
    name: "PROCESSING",
    message: "Creating job ticket",
    icon: "mdi-cog-outline",
    iconColor: "#1562af",
  },
  SENDING_PLANT: {
    name: "SENDING_PLANT",
    message: "Sending to plant",
    icon: "mdi-office-building-cog-outline",
    iconColor: "#1562af",
  },
  ERROR_SENDING_PLANT: {
    name: "ERROR_SENDING_PLANT",
    message: "Failed to send to plant.",
    icon: "mdi-office-building-remove-outline",
    iconColor: "#b00020",
  },
  RECEIVED_PLANT: {
    name: "RECEIVED_PLANT",
    message: "Sending to printer",
    icon: "mdi-office-building-marker-outline",
    iconColor: "#1562af",
  },
  ERROR_PCC: {
    name: "ERROR_PCC",
    message: "Print Control Center unavailable.",
    icon: "mdi-printer-pos-alert-outline",
    iconColor: "#b00020",
  },
  ERROR_PROCESSING: {
    name: "ERROR_PROCESSING",
    message: "Processing failed.",
    icon: "mdi-alert",
    iconColor: "#b00020",
  },
  DONE: {
    name: "DONE",
    message: "Done",
    icon: "mdi-file-document-check-outline",
    iconColor: "#007e00",
  },
};

export const useOrdersStore = defineStore("orders", {
  /**
   * Represents the state of the orders store.
   *
   * @property {Array} orders - An array to store the orders data.
   * @property {Array} productOrders - An array to store the product orders data.
   * @property {boolean} ordersLoaded - A flag indicating if the orders have been loaded.
   * @property {boolean} productOrdersLoaded - A flag indicating if the product orders have been loaded.
   * @property {number} currentPage - Current page number
   * @property {number} currentPageSize - Number of items per page
   * @property {number} ordersTotal - Total count of orders
   * @property {string} currentSortField - The current sort field
   * @property {string} currentSortOrder - The current sort order
   * @property {Object} useToast - A toast notification instance for displaying messages.
   * @property {UsersStore} useUsersStore - Users store
   * @property {RecipeStore} useRecipesStore - Recipes store
   */
  state: () => ({
    orders: [],
    productOrders: [],
    ordersLoaded: false,
    productOrdersLoaded: false,
    currentPage: 1,
    currentPageSize: 10,
    ordersTotal: 10,
    currentSortField: "created",
    currentSortOrder: "desc",
    useToast: useToast(),
    useHelper: useHelper(),
    useUsersStore: useUsersStore(),
    useRecipesStore: useRecipesStore(),
    currentFilters: reactive({
      orderId: {
        value: "",
        type: "input",
        label: "Order ID",
      },
      productId: {
        value: "",
        type: "input",
        label: "Product ID",
      },
      plantNames: {
        value: [],
        type: "multiSelect",
        label: "Plant name",
      },
      printerNames: {
        value: [],
        type: "multiSelect",
        label: "Printer name",
      },
      customerOrderId: {
        value: "",
        type: "input",
        label: "Order Number",
      },
      productNumber: {
        value: "",
        type: "input",
        label: "Product Number",
      },
      ownerName: {
        value: "",
        type: "input",
        label: "Owner Name",
      },
      createdBefore: {
        value: null,
        type: "dateInput",
        label: "Created before",
      },
      createdAfter: {
        value: null,
        type: "dateInput",
        label: "Created after",
      },
      state: {
        value: [],
        type: "multiSelect",
        label: "State",
      },
    }),
  }),

  getters: {
    /**
     * Finds the latest product order based on the created date.
     * @param {object} state The state of the productOrders store.
     * @returns {object} The latest order.
     */
    getLatestProductOrder(state) {
      return state.productOrders.reduce((latest, order) => {
        return new Date(order.created) > new Date(latest.created)
          ? order
          : latest;
      }, state.productOrders[0]);
    },
  },

  actions: {
    /**
     * Fetches the list of orders from the server and updates the state.
     *
     * Also fetches the needed users from the server and loads them into the users store.
     *
     * @async
     * @returns {Promise<number|null>} - A promise that resolves to the total amount of orders (or null).
     */
    async fetchOrders() {
      let totalOrders = null;

      try {
        // We need `view=1` param to get endpoint with 'dpu_name'
        const resultArr = ["view=1"];

        if (this.currentSortOrder && this.currentSortField) {
          const sortOrder = this.currentSortOrder === "asc" ? "+" : "-";
          resultArr.push(`sort=${sortOrder}${this.currentSortField}`);
        }

        resultArr.push(`page=${this.currentPage}`);
        resultArr.push(`size=${this.currentPageSize}`);

        const filtersParams = [];

        const filterOrderId = this.currentFilters.orderId.value;
        const filterProductId = this.currentFilters.productId.value;
        const filterProductNumber = this.currentFilters.productNumber.value;
        const filterOwnerName = this.currentFilters.ownerName.value;
        const filterPlantNames = this.currentFilters.plantNames.value;
        const filterPrinterNames = this.currentFilters.printerNames.value;
        const filterCustomerOrderId = this.currentFilters.customerOrderId.value;
        const filterCreatedBefore = this.currentFilters.createdBefore.value;
        const filterCreatedAfter = this.currentFilters.createdAfter.value;
        const filterStates = this.currentFilters.state.value;

        if (filterOrderId) {
          const parsedFilterOrderId = Number(filterOrderId) || -1;
          filtersParams.push(`id=${parsedFilterOrderId}`);
        }
        if (filterProductId) {
          const parsedFilterProductId = Number(filterProductId) || -1;
          filtersParams.push(`id=${parsedFilterProductId}`);
        }
        if (filterPlantNames.length) {
          filtersParams.push(`plant_id=${filterPlantNames.join("|")}`);
        }
        if (filterPrinterNames.length) {
          filtersParams.push(`dpu_id=${filterPrinterNames.join("|")}`);
        }
        if (filterCustomerOrderId) {
          filtersParams.push(`order_number~=${filterCustomerOrderId}`);
        }
        if (filterProductNumber) {
          filtersParams.push(`product_number~=${filterProductNumber}`);
        }
        if (filterOwnerName) {
          filtersParams.push(`owner_name~=${filterOwnerName}`);
        }
        if (filterCreatedBefore) {
          filtersParams.push(
            `created<=${new Date(filterCreatedBefore).toISOString()}`
          );
        }
        if (filterCreatedAfter) {
          filtersParams.push(
            `created>=${new Date(filterCreatedAfter).toISOString()}`
          );
        }
        if (filterStates.length) {
          filtersParams.push(`state=${filterStates.join("|")}`);
        }

        if (filtersParams.length) {
          resultArr.push(`filter=${filtersParams.join(",")}`);
        }
        let targetUrl = `${ROOT_URL}/orders`;

        if (resultArr.length) {
          targetUrl += `?${resultArr.join("&")}`;
        }
        this.ordersLoaded = false;
        const res = await api.get(targetUrl);

        // We need a set because we need to get rid of duplicate user IDs
        const userIdsSet = new Set();

        this.orders = res.data.items.map((order) => {
          userIdsSet.add(order.owner_uuid);

          return {
            ...order,
            state: ORDER_STATUS[order.state]?.name,
            stateObj: ORDER_STATUS[order.state],
          };
        });

        this.useUsersStore.fetchUsers([...userIdsSet]);

        totalOrders = res.data.total;
        this.ordersTotal = totalOrders;

        this.ordersLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return totalOrders;
    },

    /**
     * Fetches the list of orders for a specific product from the server and updates the state.
     *
     * This function constructs a query string with given parameters and makes a GET request
     * to the server to retrieve the list of orders. Upon successful retrieval, it updates the
     * product orders in the state and sets the total number of product orders. If fetching fails
     * and there is no page reload flag in session storage, it displays an error toast message.
     *
     * @async
     * @param {string} productId - The ID of the product to fetch orders for.
     * @param {string[]} [paramsArr] - An array of parameters to be passed with the request.
     * @returns {Promise<number|null>} - A promise that resolves to the total amount of product orders (or null).
     */
    async fetchOrdersByProductId(productId, paramsArr) {
      let totalProductOrders = null;

      try {
        let targetUrl = `${PRODUCTS_URL}/${productId}/orders`;

        if (paramsArr) {
          targetUrl += `?${paramsArr.join("&")}`;
        }

        this.productOrdersLoaded = false;

        const res = await api.get(targetUrl);

        this.productOrders = await Promise.all(
          res.data.items.map(async (order) => {
            const autoCodeSettings = await this.getOrderAutoCodesSettings(
              productId,
              order.id
            );

            return {
              ...order,
              state: ORDER_STATUS[order.state]?.name,
              stateObj: ORDER_STATUS[order.state],
              autocodes: autoCodeSettings,
            };
          })
        );

        totalProductOrders = res.data.total;

        this.productOrdersLoaded = true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return totalProductOrders;
    },

    /**
     * Fetches all orders for a product with their owners information in a single request.
     *
     * @param {string} productId - The ID of the product to fetch orders for.
     * @returns {Promise<Object[]>} - A promise that resolves to an array of objects with order information and owner's name and email.
     */
    async fetchAllOrdersWithOwnersInfoByProductId(productId) {
      let resultArr = [];

      try {
        let targetUrl = `${PRODUCTS_URL}/${productId}/orders?view=1&size=-1`;

        const res = await api.get(targetUrl);

        // We need a set because we need to get rid of duplicate user IDs
        const userIdsSet = new Set();

        const fetchedOrders = res.data.items.map((order) => {
          userIdsSet.add(order.owner_uuid);

          return {
            ...order,
            state: ORDER_STATUS[order.state]?.message,
          };
        });

        await this.useUsersStore.fetchUsers([...userIdsSet]);

        resultArr = fetchedOrders.map((orderObj) => {
          const orderOwner = this.useUsersStore.users[orderObj.owner_uuid];

          return {
            ...orderObj,
            ownerName: orderOwner.name,
            ownerEmail: orderOwner.email,
          };
        });
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }

      return resultArr;
    },

    /**
     * Creates a new order and returns its ID.
     *
     * This function takes an `item` object and a `productId` as parameters and creates a new
     * order with the given data. If the creation is successful, it returns the ID of the
     * newly created order. If the creation fails, it returns null and displays an error
     * toast message if there is no page reload flag in session storage.
     *
     * @async
     * @param {Object} item - The order data to create.
     * @param {string} productId - The ID of the product to create the order for.
     * @returns {Promise<number | null>} - A promise that resolves to the ID of the newly created
     * order (or null if the creation fails).
     */
    async createOrder(item, productId) {
      const order = {
        dpu_id: 1,
        ...item,
      };

      try {
        let orderId = null;

        const res = await api.post(
          `${PRODUCTS_URL}/${productId}/orders`,
          order
        );

        this.useToast.success("Order created", { timeout: 1000 });

        orderId = res.data.item.id;

        //delete all vdp_order for order_id
        await this.deleteVdpOrderSettings(productId, orderId);

        //add vdp for order
        await this.addVdpOrderSettings(productId, orderId, item.autocodes);
        return orderId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return null;
      }
    },

    /**
     * Updates an order with the given data and returns its ID.
     *
     * This function takes an `item` object and a `data` object as parameters and updates
     * the order with the given data. If the update is successful, it returns the ID of the
     * updated order. If the update fails, it returns null and displays an error toast
     * message if there is no page reload flag in session storage.
     *
     * If the order's status is already set to 'DONE', it throws an error with the message
     * "Status: DONE" and returns null.
     *
     * @async
     * @param {Object} item - The order data to update.
     * @param {Object} data - The new data to update the order with.
     * @returns {Promise<number | null>} - A promise that resolves to the ID of the updated
     * order (or null if the update fails).
     */
    async updateOrder(item, data) {
      try {
        if (item.state === PRODUCT_STATE.DONE) {
          throw new Error(`Status: ${PRODUCT_STATE.DONE}`);
        }

        let orderId = null;

        const res = await api.put(
          `${PRODUCTS_URL}/${item.product_id}/orders/${item.id}`,
          data
        );

        this.useToast.success("Order updated", {
          timeout: 1000,
        });

        orderId = res.data.item.id;

        const normalizedAutoCodes = data.autocodes.map((code) => ({
          ...code,
          product_vdp_id: this.useHelper.normalizeToNumber(code.id),
          incrementStart: this.useHelper.normalizeToNumber(code.incrementStart),
          incrementStep: this.useHelper.normalizeToNumber(code.incrementStep),
        }));

        await this.deleteVdpOrderSettings(item.product_id, orderId);
        await this.addVdpOrderSettings(
          this.useHelper.normalizeToNumber(item.product_id),
          orderId,
          normalizedAutoCodes
        );

        return orderId;
      } catch (e) {
        if (
          error instanceof Error &&
          error.message === `Status: ${PRODUCT_STATE.DONE}`
        ) {
          if (!sessionStorage.getItem("pageReload")) {
            this.useToast.warning(
              `Cannot update order because its status is already '${PRODUCT_STATE.DONE}'`,
              {
                timeout: 2000,
              }
            );
          }
        } else {
          if (!sessionStorage.getItem("pageReload")) {
            this.useToast.error(error.response.data.error.message, {
              timeout: 2500,
            });
          }
        }

        return null;
      }
    },

    /**
     * Deletes an order and returns its ID.
     *
     * This function sends a DELETE request to delete the order identified by the item's ID.
     * Upon successful deletion, it shows a success toast message and returns the ID of the deleted order.
     *
     * If the delete operation fails and there is no page reload flag in session storage,
     * it displays an error toast message indicating the failure to delete the order.
     *
     * @async
     * @param {Object} item - The order data containing the ID of the order to delete.
     * @returns {Promise<number | null>} - A promise that resolves to the ID of the deleted order,
     * or null if the delete fails.
     */
    async deleteOrder(item) {
      try {
        const res = await api.delete(
          `${PRODUCTS_URL}/${item.product_id}/orders/${item.id}`
        );

        if (res.data.status === "success") {
          this.useToast.success("Order deleted", {
            timeout: 2000,
          });
        }

        return item.id;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Prints an order and returns its ID.
     *
     * This function sends a POST request to print the order identified by the item's ID.
     * Upon successful printing, it shows a success toast message and returns the ID of the printed order.
     *
     * If the print operation fails and there is no page reload flag in session storage,
     * it displays an error toast message indicating the failure to print the order.
     *
     * @async
     * @param {Object} item - The order data containing the ID of the order to print.
     * @returns {Promise<number | null>} - A promise that resolves to the ID of the printed order,
     * or null if the print fails.
     */
    async printOrder(item) {
      try {
        let orderId = null;

        const res = await api.post(
          `${PRODUCTS_URL}/${item.product_id}/orders/${item.id}/print`
        );

        if (res.data.status === "success") {
          this.useToast.success("Order queued", {
            timeout: 1000,
          });
        }

        orderId = res.data.item.id;

        return orderId;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },

    /**
     * Updates the order data in the `orders` array by comparing the order ID.
     * If the ID matches, updates `dpu_id` and `order_number` of the corresponding item.
     *
     * @param {Object} order - The order with a new data.
     */
    updateOrderData(order) {
      const index = this.products.findIndex((item) => item.id === order.id);

      if (index !== -1) {
        this.products.splice(index, 1, {
          ...order,
          dpu_id: order.dpu_id,
          order_number: order.order_number,
          state: ORDER_STATUS[order.state]?.name,
          stateObj: ORDER_STATUS[order.state],
        });
      }
    },

    /**
     * Updates the order data in the `productOrders` array by comparing the order ID.
     * If the ID matches, updates `dpu_id` and `order_number` of the corresponding item.
     *
     * @param {Object} productOrder - The object with updated product ordering information.
     */
    updateProductOrderById(productOrder) {
      this.productOrders = this.productOrders.map((item) => {
        if (item.id === productOrder.id) {
          const recipeObj = this.useRecipesStore.recipesNames.find(
            (r) => r.value === productOrder.recipe_id
          );
          return {
            ...item,
            dpu_id: productOrder.dpu_id,
            order_number: productOrder.order_number,
            state: ORDER_STATUS[productOrder.state]?.name,
            stateObj: ORDER_STATUS[productOrder.state],
            recipe_id: productOrder.recipe_id,
            recipe_name: recipeObj.text
          };
        }
        return item;
      });
    },

    async deleteVdpOrderSettings(product_id, order_id) {
      try {
        const res = await api.delete(
          `${PRODUCTS_URL}/${product_id}/orders/${order_id}/vdp`
        );

        return true;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }
      }
    },
    async addVdpOrderSettings(product_id, order_id, settings) {
      try {
        const res = await api.post(
          `${PRODUCTS_URL}/${product_id}/orders/${order_id}/vdp`,
          settings
        );

        return res;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return false;
      }
    },
    async getOrderAutoCodesSettings(product_id, order_id) {
      try {
        const res = await api.get(
          `${PRODUCTS_URL}/${product_id}/orders/${order_id}/vdp`
        );
        // Transform the data
        const result = Object.values(
          res.data.items.reduce((acc, item) => {
            const id =
              typeof item.product_vdp_id === "string"
                ? parseInt(item.product_vdp_id, 10)
                : item.product_vdp_id;

            // If the object for this `product_vdp_id` doesn't exist, create it
            if (!acc[id]) {
              acc[id] = {
                id: item.id,
                product_vdp_id:
                  typeof item.product_vdp_id === "string"
                    ? parseInt(item.product_vdp_id, 10)
                    : item.product_vdp_id,
              };
            }

            // Add the `step` or `start` key based on `key_name`
            acc[id][item.key_name] = item.key_value;

            return acc;
          }, {})
        );
        return result;
      } catch (error) {
        if (!sessionStorage.getItem("pageReload")) {
          this.useToast.error(error.response.data.error.message, {
            timeout: 2500,
          });
        }

        return false;
      }
    },

  },
});
