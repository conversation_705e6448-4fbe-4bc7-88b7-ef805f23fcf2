<template>
  <!-- Brandowners table -->
  <DataTable
    title="Customers"
    :sortBy
    :items="brandowners"
    :itemsLength="useBrandownersStore.brandownersTotal"
    :itemsPerPage="currentPageSize"
    :currentPage="currentPage"
    :headers
    :formFields
    :loading="!useBrandownersStore.brandownersLoaded"
    :hasActiveFilters="!!activeFilterChips.length"
    emptyMsg="No customers available"
    @removeAllFilters="removeAllFilters"
    @updateOptions="updateBrandownersOptions"
  >
    <!-- Create action (button with modal) -->
    <template
      #createAction="{
        setSelectedItem,
        formData,
        resetFormData,
        parseFormData,
        handleUpload,
      }"
    >
      <ActionCreateItem
        modalBtnText="Create customer"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        :resetFormData="resetFormData"
        :parseFormData="parseFormData"
        :handleUpload="handleUpload"
        @createItem="(item, closeModal) => createBrandowner(item, closeModal)"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      >
        <!-- Modal for brandowner creation -->
        <template #createActionModalContent="{ formData, close, createItem }">
          <v-divider />

          <!-- Form for brandowner creation -->
          <v-form @submit.prevent="createItem">
            <!-- Inputs for brandowner fields -->
            <template v-for="item in formData" :key="item">
              <v-text-field
                v-if="!item.readonly && item.type !== 'file'"
                v-model="item.value"
                :label="item.label"
                :type="item.type"
                :maxlength="item.maxlength"
              />
            </template>

            <!-- Buttons for either creating brandowner or closing modal -->
            <div class="form-buttons">
              <BaseButton class="mt-2" @click="close"> Close </BaseButton>
              <BaseButton
                class="mt-2"
                btnType="primary"
                type="submit"
                :loading="isBrandownerCreating"
              >
                Submit
              </BaseButton>
            </div>
          </v-form>
        </template>
      </ActionCreateItem>
    </template>

    <!-- Chips containing active filters -->
    <template #filtersChipsSlot>
      <v-chip
        class="vuetify-chip-wrapper"
        v-for="filterChip in activeFilterChips"
        :key="filterChip.key"
        closable
        @click:close="
          removeFilter(
            filterChip.filterType,
            filterChip.filterKey,
            filterChip.value
          )
        "
      >
        {{ filterChip.label }}
      </v-chip>
    </template>

    <!-- Filters input fields -->
    <template #filtersInputsSlot>
      <BaseInput
        v-if="config.tablesColumns.brandowners.id"
        v-model="currentFilters.id.value"
        :label="currentFilters.id.label"
      />
      <BaseInput
        v-if="config.tablesColumns.brandowners.name"
        v-model="currentFilters.name.value"
        :label="currentFilters.name.label"
      />
      <BaseInput
        v-if="config.tablesColumns.brandowners.description"
        v-model="currentFilters.description.value"
        :label="currentFilters.description.label"
      />
      <BaseInput
        v-if="config.tablesColumns.brandowners.address"
        v-model="currentFilters.address.value"
        :label="currentFilters.address.label"
      />
      <BaseInput
        v-if="config.tablesColumns.brandowners.contact"
        v-model="currentFilters.contact.value"
        :label="currentFilters.contact.label"
      />
      <BaseInput
        v-if="config.tablesColumns.brandowners.taxid"
        v-model="currentFilters.taxId.value"
        :label="currentFilters.taxId.label"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.brandowners.archived"
        v-model="currentFilters.archived.value"
        :label="currentFilters.archived.label"
        :options="archivedValuesOptions"
      />
    </template>

    <!-- Actions (view info, delete, edit) -->
    <template
      #actions="{
        item,
        setSelectedItem,
        formData,
        resetFormData,
        parseFormData,
      }"
    >
      <ActionViewItemInfo
        :item="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <ActionDeleteItem
        :isActionVisible="false"
        entityLabel="brandowner"
        :isDeleting="isBrandownerDeleting"
        :item="item"
        :setSelectedItem="setSelectedItem"
        @deleteItem="(item, closeModal) => deleteBrandowner(item, closeModal)"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <ActionEditItem
        :item="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        :resetFormData="resetFormData"
        :parseFormData="parseFormData"
        @updateItem="
          (item, data, closeModal) => updateBrandowner(item, data, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      >
        <!-- Form for brandowner editing -->
        <template #updateActionModalContent="{ formData, close, updateItem }">
          <v-sheet class="selected-item" :width="420">
            <!-- Title of modal -->
            <p class="selected-item__title">Edit item</p>

            <v-divider />

            <v-form @submit.prevent="updateItem(formData, item, close)">
              <!-- Inputs for brandowner fields -->
              <template v-for="(item, index) in formData" :key="index">
                <v-text-field
                  v-if="!item.readonly && item.type !== 'file'"
                  v-model="item.value"
                  :label="item.label"
                  :type="item.type"
                  :maxlength="item.maxlength"
                />
              </template>

              <!-- Buttons for either updating brandowner or closing modal -->
              <div class="form-buttons">
                <BaseButton class="mt-2" @click="close"> Close </BaseButton>
                <BaseButton
                  class="mt-2"
                  btnType="primary"
                  type="submit"
                  :loading="isBrandownerUpdating"
                >
                  Submit
                </BaseButton>
              </div>
            </v-form>
          </v-sheet>
        </template>
      </ActionEditItem>

      <ActionActivateBrandowner
        :isUpdating="isBrandownerUpdating"
        :brandowner="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        :parseFormData="parseFormData"
        @updateBrandowner="
          (item, data, closeModal) => updateBrandowner(item, data, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <ActionDeactivateBrandowner
        :isUpdating="isBrandownerUpdating"
        :brandowner="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        :parseFormData="parseFormData"
        @updateBrandowner="
          (item, data, closeModal) => updateBrandowner(item, data, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />
    </template>
  </DataTable>
</template>

<script>
import config from "@/assets/config.json";
import { reactive } from "vue";
import { useDebounceFn } from "@vueuse/core";
import DataTable from "@/components/DataTable.vue";
import BaseButton from "@/components/common/BaseButton.vue";
import BaseInput from "@/components/common/BaseInput.vue";
import BaseAutocomplete from "@/components/common/BaseAutocomplete.vue";
import ActionCreateItem from "@/components/actions/ActionCreateItem.vue";
import ActionViewItemInfo from "@/components/actions/ActionViewItemInfo.vue";
import ActionDeleteItem from "@/components/actions/ActionDeleteItem.vue";
import ActionEditItem from "@/components/actions/ActionEditItem.vue";
import ActionActivateBrandowner from "@/components/actions/ActionActivateBrandowner.vue";
import ActionDeactivateBrandowner from "@/components/actions/ActionDeactivateBrandowner.vue";
import { useBrandownersStore } from "@/store/brandowners";
import useTableColumns from "@/composables/useTableColumns";
import useTableFilters from "@/composables/useTableFilters";
import useWebSocket from "@/composables/useWebSocket";
import { useRecipesStore } from "@/store/recipes";

export default {
  /**
   * Setup function for the BrandownersView component.
   *
   * Initializes and returns reactive variables and functions related to brandowner filters.
   *
   * @returns {Object} An object containing:
   * - `headers`: List of required headers for the table.
   * - `currentFilters`: A reactive object representing the current filters applied to the table.
   * - `activeFilterChips`: An array of active filter chips based on current filters.
   * - `debouncedFetchData`: A debounced function to handle fetching data with updated filters.
   * - `removeFilter`: A function to remove a specific filter from the current filters.
   * - `removeAllFilters`: A function to clear all active filters.
   */

  setup() {
    const { headers } = useTableColumns("brandowners", [
      { title: "ID", key: "id" },
      { title: "Name", key: "name" },
      { title: "Description", key: "description" },
      { title: "Local Address", key: "address" },
      { title: "Mail Contact", key: "contact" },
      { title: "Customer ID", key: "taxid" },
      { title: "Activation status", key: "archived" },
    ]);

    const { currentFilters } = useBrandownersStore();

    const {
      activeFilterChips,
      archivedValuesOptions,
      removeFilter,
      removeAllFilters,
    } = useTableFilters(currentFilters);

    return {
      headers,
      currentFilters,
      activeFilterChips,
      archivedValuesOptions,
      removeFilter,
      removeAllFilters,
    };
  },

  components: {
    DataTable,
    BaseButton,
    BaseInput,
    BaseAutocomplete,
    ActionCreateItem,
    ActionViewItemInfo,
    ActionDeleteItem,
    ActionEditItem,
    ActionActivateBrandowner,
    ActionDeactivateBrandowner,
  },

  /**
   * Data of the component.
   *
   * @typedef {Object} Data
   * @property {Object} config - Configuration object for the project
   * @property {boolean} isSomeModalOpened - Flag indicating if a modal is opened
   * @property {BrandownersStore} useBrandownersStore - Brandowners store
   * @property {boolean} isBrandownerCreating - Is brandowner creating
   * @property {boolean} isBrandownerUpdating - Is brandowner updating
   * @property {boolean} isBrandownerDeleting - Is brandowner deleting
   * @property {Array<Object>} formFields - Form fields configuration
   */
  data() {
    return {
      config: config,
      isSomeModalOpened: false,
      useBrandownersStore: useBrandownersStore(),
      isBrandownerCreating: false,
      isBrandownerUpdating: false,
      isBrandownerDeleting: false,
      formFields: [
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "name",
          label: "Customer name",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxlength: 50,
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "description",
          label: "Description",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxlength: 100,
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "address",
          label: "Local Address",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxlength: 70,
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "contact",
          label: "Mail Contact",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxlength: 50,
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "taxid",
          label: "Customer ID",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxlength: 25,
        },
      ],
    };
  },

  computed: {
    currentPage() {
      return this.useBrandownersStore.currentPage;
    },
    currentPageSize() {
      return this.useBrandownersStore.currentPageSize;
    },
    sortBy: {
      /**
       * Computes the sorting criteria for products.
       *
       * @returns {Array<Object>} An array with a single object containing the
       *                          sorting 'key' and 'order'.
       */
      get() {
        return [
          {
            key: this.useBrandownersStore.currentSortField ?? "created",
            order: this.useBrandownersStore.currentSortOrder ?? "desc",
          },
        ];
      },
      set() {},
    },

    /**
     * Returns the list of brandowners.
     *
     * @returns {Array} List of brandowners.
     */
    brandowners() {
      return this.useBrandownersStore.brandowners.map((brandowner) => {
        return {
          ...brandowner,
          archived: brandowner.archived ? "Deactivated" : "Activated",
        };
      });
    },
  },

  watch: {
    /**
     * Watches for changes in the currentFilters reactive property.
     *
     * Calls the handleFetchingBrandowners method whenever the currentFilters
     * property changes. The handleFetchingBrandowners method fetches data for
     * the brandowners table.
     */
    currentFilters: {
      handler: useDebounceFn(
        function () {
          this.handleFetchingBrandowners();
        },
        500,
        { maxWait: 5000 }
      ),
      deep: true,
    },
  },

  methods: {
    /**
     * Handles the opening of any modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleModalOpening() {
      this.isSomeModalOpened = true;
    },

    /**
     * Handles the closing of any modal on the page by setting the isSomeModalOpened flag to false.
     */
    handleModalClosing() {
      this.isSomeModalOpened = false;
    },

    /**
     * Creates a new brandowner using the provided item data.
     *
     * Fetches the updated list of brandowners upon successful creation
     * and displays a success message. If creation fails, displays an
     * error message and returns false.
     *
     * @async
     * @param {Object} item - The brandowner data to create.
     * @param {function} closeModal - A closeModal to be called after
     * the brandowner has been created.
     * @returns {Promise<boolean | undefined>} - A promise that resolves to
     * true if the brandowner was created successfully, false if creation
     * failed, or undefined if the request was not successful.
     */
    async createBrandowner(item, closeModal) {
      this.isBrandownerCreating = true;
      const result = await this.useBrandownersStore.createBrandowner(item);
      this.isBrandownerCreating = false;

      if (result) {
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the brandowners table with active filters & sorting after action
        await this.handleFetchingBrandowners();
      }
    },

    /**
     * Updates a brandowner using the provided item data and returns its ID.
     *
     * Upon successful update, it displays a success toast message and
     * returns the ID of the updated brandowner. If the update fails, it
     * displays an error toast message unless the page reload flag is set
     * in session storage.
     *
     * @async
     * @param {Object} item - The brandowner data to update.
     * @param {Object} data - The data to update the brandowner with.
     * @param {function} closeModal - A closeModal to be called after
     * the brandowner has been updated.
     * @returns {Promise<number | undefined>} - A promise that resolves to
     * the ID of the updated brandowner (or undefined if the update fails).
     */
    async updateBrandowner(item, data, closeModal) {
      this.isBrandownerUpdating = true;
      const result = await this.useBrandownersStore.updateBrandowner(
        item,
        data
      );
      this.isBrandownerUpdating = false;

      if (result) {
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the brandowners table with active filters & sorting after action
        await this.handleFetchingBrandowners();
      }
    },

    /**
     * Deletes a brandowner using the provided item data and returns its ID.
     *
     * Upon successful deletion, it displays a success toast message and
     * returns the ID of the deleted brandowner. If the deletion fails, it
     * displays an error toast message unless the page reload flag is set
     * in session storage.
     *
     * @async
     * @param {Object} item - The brandowner data to delete.
     * @param {function} closeModal - A closeModal to be called after
     * the brandowner has been deleted.
     * @returns {Promise<number | undefined>} - A promise that resolves to
     * the ID of the deleted brandowner (or undefined if the deletion fails).
     */
    async deleteBrandowner(item, closeModal) {
      this.isBrandownerDeleting = true;
      const result = await this.useBrandownersStore.deleteBrandowner(item);
      this.isBrandownerDeleting = false;

      if (result) {
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the brandowners table with active filters & sorting after action
        await this.handleFetchingBrandowners();
      }
    },

    /**
     * Fetches the list of brandowners based on the current page, page size, and sorting parameters.
     * Does not perform the fetch if any modal is currently open on the page.
     *
     * @async
     */
    async handleFetchingBrandowners() {
      if (this.isSomeModalOpened) {
        return;
      }

      await this.useBrandownersStore.fetchBrandowners();
    },

    /**
     * Updates the options for the brandowners table, including page size,
     * current page, and sorting parameters. Triggers a refresh of the
     * brandowners list if the data is already loaded, and resets the
     * fetch interval.
     *
     * @param {Object} options - Options object containing the updated
     * pagination and sorting parameters.
     */
    updateBrandownersOptions(options) {
      this.useBrandownersStore.currentPageSize = options.itemsPerPage;
      this.useBrandownersStore.currentPage = options.page;

      if (options.sortBy[0]) {
        this.useBrandownersStore.currentSortField = options.sortBy[0].key;
        this.useBrandownersStore.currentSortOrder = options.sortBy[0].order;
      } else {
        this.useBrandownersStore.currentSortField = null;
        this.useBrandownersStore.currentSortOrder = null;
      }

      this.handleFetchingBrandowners();
    },
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   * Sets up a WebSocket connection and listens for incoming messages.
   */

  mounted() {
    useWebSocket({
      onMessage: (data) => {
        if (data.type === "brandowners") {
          if (data.action === "update"){
            this.useBrandownersStore.updateBrandownerData(data.data);
          } else{
            this.useBrandownersStore.fetchBrandowners();
          }

        }
      },
    });
  },
};
</script>

<style lang="scss" scoped></style>
