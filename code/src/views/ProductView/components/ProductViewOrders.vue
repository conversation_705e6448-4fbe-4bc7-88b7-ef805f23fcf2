<template>
  <!-- View with orders -->
  <v-sheet class="px-4 py-4 info">
    <div class="info__block">
      <!-- Label -->
      <div class="info__header">
        <h2 class="info__title">Orders</h2>
      </div>

      <!-- Info text that appears when order cannot be created (due to product state) -->
      <p v-if="!orderCanBeCreated">
        You are not able to create an order because proofing for this product is
        not finished
      </p>

      <!-- Orders table -->
      <DataTable
        emptyMsg="No orders available"
        :loading="!useOrdersStore.productOrdersLoaded"
        :sortBy
        :items="orders"
        :itemsLength="ordersTotal"
        :itemsPerPage="currentPageSize"
        :currentPage="currentPage"
        :headers
        :formFields
        :hasActiveFilters="!!activeFilterChips.length"
        :selectedProductId="selectedProductId"
        @uploadFiles="(files, id) => uploadFiles(files, id)"
        @removeAllFilters="removeAllFilters"
        @updateOptions="updateOrdersOptions"
      >
        <!-- Create action (button with modal) -->
        <template
          #createAction="{
            setSelectedItem,
            formData,
            resetFormData,
            parseFormData,
            handleUpload,
          }"
        >
          <ActionCreateItem
            modalBtnText="Create order"
            :isModalBtnDisabled="!orderCanBeCreated"
            :setSelectedItem="setSelectedItem"
            :formData="formData"
            :resetFormData="resetFormData"
            :parseFormData="parseFormData"
            :handleUpload="handleUpload"
            @createItem="(item, closeModal) => createOrder(item, closeModal)"
            @onOpenModalEvent="handleCreateModalOpening"
            @onCloseModalEvent="handleModalClosing"
          >
            <!-- Modal for order creation -->
            <template
              #createActionModalContent="{ formData, close, createItem }"
            >
              <v-divider v-if="vardataPresent" />

              <!-- Timeline with statuses of loaded CSV files (if vardata is present) -->
              <FormTimeline
                v-if="vardataPresent"
                :step="step"
                :firstField="createOrderStatus"
                :secondField="uploadVardataStatus"
              />

              <v-divider />

              <!-- Form for creating orders -->
              <v-form v-if="step === 1" @submit.prevent="createItem(close)">
                <template v-for="(item, index) in formData" :key="item">
                  <!-- Input for order number -->
                  <BaseInput
                    v-if="item.visibility.createAction && item.type === 'text'"
                    v-model="item.value"
                    :label="item.label"
                    :type="item.type"
                    :disabled="item.disabled"
                    :maxlength="item.maxlength"
                  />

                  <!-- Either select for plant name or select for printer name -->
                  <BaseAutocomplete
                    v-else-if="
                      item.visibility.createAction && item.type === 'select'
                    "
                    :multiple="false"
                    v-model="item.value"
                    :label="item.label"
                    :options="item.options"
                    :isDisabled="isBaseSelectDisabled(item, formData)"
                    :isLoading="isBaseSelectLoading(item, formData)"
                    @onSelectChange="onSelectChange(item)"
                  />
                  <BaseInput
                    v-if="
                      item.visibility.createAction &&
                      item.type === 'number' &&
                      hasAutoCodes
                    "
                    v-model="item.value"
                    :label="item.label"
                    :type="item.type"
                    :maxlength="item.maxlength"
                  />
                  <EditableDataTable
                    v-if="
                      item.visibility.createAction &&
                      item.type === 'editableDataTable' &&
                      hasAutoCodes
                    "
                    v-model="item.value"
                    :label="item.label"
                    @input="item.value = $event"
                  />
                </template>

                <!-- Buttons for either creating order or closing modal -->
                <div class="form-buttons mt-2">
                  <BaseButton @click="close">Close</BaseButton>
                  <BaseButton
                    type="submit"
                    btnType="primary"
                    :loading="isOrderCreating"
                    :disabled="
                      formData.some(
                        (item) => !item.value && item.visibility.createAction
                      )
                    "
                  >
                    Submit
                  </BaseButton>
                </div>
              </v-form>

              <!-- Form for uploading CSV files (if vardata is present and step is 2) -->
              <v-form
                v-else-if="step === 2 && vardataPresent"
                @submit.prevent="uploadCSVFile(close)"
              >
                <!-- Preview of uploaded CSV files -->
                <v-sheet
                  class="preview"
                  v-if="csvData.length && csvHeaders.length"
                >
                  <div class="d-flex ga-2">
                    <div>
                      <span class="font-weight-bold">Headers:</span>
                      {{ this.csvHeaders.length }}
                    </div>

                    <div>
                      <span class="font-weight-bold">Rows:</span>
                      {{ this.csvData.length }}
                    </div>
                  </div>

                  <!-- Table consisting of CSV files headers and rows -->
                  <BaseTable :tableHeaders="csvHeaders" :tableData="csvData" />
                </v-sheet>

                <!-- Uploader for CSV files -->
                <FileUploader
                  v-else
                  acceptType="text/csv"
                  :multiple="false"
                  :uploadedFiles="uploadedFiles"
                  @downloadFile="(id) => $emit('downloadFile', id)"
                  @deleteFile="(id) => $emit('deleteFile', id)"
                  @uploadFiles="(files) => readCSVFile(files)"
                  ref="fileUploaderRef"
                />

                <!-- Buttons for either submitting CSV files or resetting them from uploader -->
                <div class="form-buttons">
                  <BaseButton
                    v-if="csvData.length && csvHeaders.length"
                    @click="clearFile"
                  >
                    Reset
                  </BaseButton>

                  <BaseButton @click="close">Close</BaseButton>

                  <BaseButton
                    v-if="isDataValidated"
                    type="submit"
                    btnType="primary"
                    :loading="areCsvFilesUploading"
                  >
                    Submit
                  </BaseButton>
                </div>
              </v-form>
            </template>
          </ActionCreateItem>
        </template>

        <!-- Chips containing active filters -->
        <template #filtersChipsSlot>
          <v-chip
            class="vuetify-chip-wrapper"
            v-for="filterChip in activeFilterChips"
            :key="filterChip.key"
            closable
            @click:close="
              removeFilter(
                filterChip.filterType,
                filterChip.filterKey,
                filterChip.value
              )
            "
          >
            {{ filterChip.label }}
          </v-chip>
        </template>

        <!-- Filters input fields -->
        <template #filtersInputsSlot>
          <BaseInput
            v-if="config.tablesColumns.productOrders.id"
            v-model="currentFilters.orderId.value"
            :label="currentFilters.orderId.label"
          />
          <BaseAutocomplete
            v-if="config.tablesColumns.orders.plant_name"
            v-model="currentFilters.plantNames.value"
            :label="currentFilters.plantNames.label"
            :options="usePlantsStore.plantsNames"
            :isLoading="!usePlantsStore.plantsNamesLoaded"
          />
          <BaseAutocomplete
            v-if="config.tablesColumns.productOrders.dpu_name"
            v-model="currentFilters.printerNames.value"
            :label="currentFilters.printerNames.label"
            :options="usePlantsStore.dpusNames"
            :isLoading="!usePlantsStore.dpusNamesLoaded"
          />
          <BaseInput
            v-if="config.tablesColumns.productOrders.order_number"
            v-model="currentFilters.customerOrderId.value"
            :label="currentFilters.customerOrderId.label"
          />
          <BaseInput
            v-if="config.tablesColumns.productOrders.owner_name"
            v-model="currentFilters.ownerName.value"
            :label="currentFilters.ownerName.label"
          />
          <BaseDateInput
            v-if="config.tablesColumns.productOrders.created"
            v-model="currentFilters.createdBefore.value"
            :label="currentFilters.createdBefore.label"
          />
          <BaseDateInput
            v-if="config.tablesColumns.productOrders.created"
            v-model="currentFilters.createdAfter.value"
            :label="currentFilters.createdAfter.label"
          />
          <BaseAutocomplete
            v-if="config.tablesColumns.productOrders.state"
            v-model="currentFilters.state.value"
            :label="currentFilters.state.label"
            :options="orderStateOptions"
          />
          <BaseAutocomplete
            v-if="config.tablesColumns.productOrders.recipe_name"
            v-model="currentFilters.papertype.value"
            :label="currentFilters.papertype.label"
            :options="useRecipesStore.recipesNames"
          />
        </template>

        <!-- Actions (view info, delete, edit, print, download jobticket) -->
        <template
          #actions="{
            item,
            setSelectedItem,
            formData,
            resetFormData,
            parseFormData,
          }"
        >
          <ActionViewItemInfo
            :item="item"
            :setSelectedItem="setSelectedItem"
            :formData="[...formData, ownerName, ownerEmail]"
            @selectItem="(item) => selectOrder(item)"
            @onOpenModalEvent="handleModalOpening"
            @onCloseModalEvent="handleModalClosing"
          >
            <!-- Buttons for showing order preview (with modal), located inside actions list of each table row -->
            <template #viewActionContentPreview>
              <v-sheet
                class="preview"
                v-if="csvData.length && csvHeaders.length && isCSVLoaded"
              >
                <div class="d-flex ga-2">
                  <div>
                    <span class="font-weight-bold">Headers:</span>
                    {{ this.csvHeaders.length }}
                  </div>

                  <div>
                    <span class="font-weight-bold">Rows:</span>
                    {{ this.csvData.length }}
                  </div>
                </div>

                <!-- Table consisting of CSV files headers and rows -->
                <BaseTable :tableHeaders="csvHeaders" :tableData="csvData" />
              </v-sheet>

              <!-- Label inside preview modal when no data is available -->
              <v-sheet
                v-else-if="
                  !(csvData.length && csvHeaders.length) && isCSVLoaded
                "
                class="d-flex justify-center align-center"
              >
                No CSV data is available
              </v-sheet>

              <!-- Label inside preview modal when data is still loading -->
              <v-sheet
                v-else-if="
                  !(csvData.length && csvHeaders.length) && !isCSVLoaded
                "
                class="d-flex justify-center align-center ga-3"
              >
                Checking for CSV data
                <v-progress-circular
                  color="#1976D2"
                  height="4"
                  indeterminate
                  rounded
                />
              </v-sheet>

              <!-- Button inside preview modal which allows user to download order CSV file -->
              <div
                v-if="csvData.length && csvHeaders.length && isCSVLoaded"
                class="form-buttons mt-2"
              >
                <BaseButton
                  btnType="primary"
                  :loading="isVardataDownloading"
                  :disabled="!(csvData.length || csvHeaders.length)"
                  @click="$emit('downloadOrder', activeOrderId)"
                >
                  Download
                </BaseButton>
              </div>
            </template>
          </ActionViewItemInfo>

          <ActionDeleteItem
            :isActionVisible="deletableStates.includes(item.state)"
            entityLabel="order"
            :isDeleting="isOrderDeleting"
            :item="item"
            :setSelectedItem="setSelectedItem"
            @deleteItem="(item, closeModal) => deleteOrder(item, closeModal)"
            @onOpenModalEvent="handleModalOpening"
            @onCloseModalEvent="handleModalClosing"
          />

          <ActionEditItem
            :isActionVisible="
              item.state !== 'DONE' &&
              item.state !== 'SENDING_PLANT' &&
              item.state !== 'PROCESSING' &&
              item.state !== 'RECEIVED_PLANT'
            "
            :item="item"
            :setSelectedItem="setSelectedItem"
            :formData="formData"
            :resetFormData="resetFormData"
            :parseFormData="parseFormData"
            @updateItem="
              (item, data, closeModal) => updateOrder(item, data, closeModal)
            "
            @onOpenModalEvent="handleEditModalOpening"
            @onCloseModalEvent="handleModalClosing"
          >
            <!-- Button for editing orders (with modal), located inside actions list of each table row -->
            <template
              #updateActionModalContent="{ formData, close, updateItem }"
            >
              <v-sheet class="selected-item" :width="420">
                <p class="selected-item__title">Edit item</p>

                <v-divider />

                <!-- Form for editing orders -->
                <v-form @submit.prevent="updateItem(formData, item, close)">
                  <template v-for="(item, index) in formData" :key="index">
                    <!-- Input for order number -->
                    <BaseInput
                      v-if="
                        item.visibility.createAction && item.type === 'text'
                      "
                      v-model="item.value"
                      :label="item.label"
                      :type="item.type"
                      :disabled="item.disabled"
                    />

                    <!-- Either select for plant name or select for printer name -->
                    <BaseAutocomplete
                      v-else-if="
                        item.visibility.editAction === true &&
                        item.type === 'select'
                      "
                      :multiple="false"
                      v-model="item.value"
                      :label="item.label"
                      :options="item.options"
                      :isDisabled="isBaseSelectDisabled(item, formData)"
                      :isLoading="isBaseSelectLoading(item, formData)"
                      @onSelectChange="onSelectChange(item)"
                    />
                    <BaseInput
                      v-if="
                        item.visibility.createAction &&
                        item.type === 'number' &&
                        hasAutoCodes
                      "
                      v-model="item.value"
                      :label="item.label"
                      :type="item.type"
                      :maxlength="item.maxlength"
                    />
                    <EditOrderAutoCode
                      v-if="
                        item.visibility.createAction &&
                        item.type === 'editableDataTable' &&
                        hasAutoCodes
                      "
                      v-model="item.value"
                      :label="item.label"
                      @input="item.value = $event"
                      :formData="formData"
                      :productId="selectedProductId"
                    />
                  </template>

                  <!-- Preview of uploaded CSV files -->
                  <v-sheet
                    class="preview"
                    v-if="csvData.length && csvHeaders.length"
                  >
                    <div class="d-flex ga-2">
                      <div>
                        <span class="font-weight-bold">Headers:</span>
                        {{ this.csvHeaders.length }}
                      </div>

                      <div>
                        <span class="font-weight-bold">Rows:</span>
                        {{ this.csvData.length }}
                      </div>
                    </div>

                    <BaseTable
                      :tableHeaders="csvHeaders"
                      :tableData="csvData"
                    />
                  </v-sheet>

                  <!-- Uploader for CSV files -->
                  <FileUploader
                    v-else-if="vardataPresent"
                    acceptType="text/csv"
                    :multiple="false"
                    :uploadedFiles="uploadedFiles"
                    @downloadFile="(id) => $emit('downloadFile', id)"
                    @deleteFile="(id) => $emit('deleteFile', id)"
                    @uploadFiles="(files) => readCSVFile(files)"
                    ref="fileUploaderRef"
                  />

                  <!-- Buttons for either submitting order editing or closing modal -->
                  <div class="form-buttons mt-2">
                    <BaseButton @click="close">Close</BaseButton>
                    <BaseButton
                      btnType="primary"
                      type="submit"
                      :loading="isOrderUpdating"
                    >
                      Submit
                    </BaseButton>
                  </div>
                </v-form>
              </v-sheet>
            </template>
          </ActionEditItem>

          <ActionPrintOrder
            :isPerforming="isOrderPrinting"
            :order="item"
            :setSelectedItem="setSelectedItem"
            @printOrder="
              (order, closeModal) => checkVardataPresence(order, closeModal)
            "
            @onOpenModalEvent="handleModalOpening"
            @onCloseModalEvent="handleModalClosing"
          />

          <ActionDownloadOrderJobticket
            :isDownloading="isJobticketDownloading"
            :order="item"
            :setSelectedItem="setSelectedItem"
            @downloadJobticket="
              (order, closeModal) => downloadJobticket(order, closeModal)
            "
            @onOpenModalEvent="handleModalOpening"
            @onCloseModalEvent="handleModalClosing"
          />
        </template>
      </DataTable>
    </div>
  </v-sheet>
</template>

<script>
import config from "@/assets/config.json";
import { reactive } from "vue";
import { useDebounceFn } from "@vueuse/core";
import JSZip from "jszip";
import { useToast } from "vue-toastification";
import DataTable from "@/components/DataTable.vue";
import FileUploader from "@/components/FileUploader.vue";
import BaseTable from "@/components/common/BaseTable.vue";
import BaseButton from "@/components/common/BaseButton.vue";
import BaseInput from "@/components/common/BaseInput.vue";
import BaseAutocomplete from "@/components/common/BaseAutocomplete.vue";
import BaseDateInput from "@/components/common/BaseDateInput.vue";
import FormTimeline from "@/components/FormTimeline.vue";
import ActionCreateItem from "@/components/actions/ActionCreateItem.vue";
import ActionViewItemInfo from "@/components/actions/ActionViewItemInfo.vue";
import ActionDeleteItem from "@/components/actions/ActionDeleteItem.vue";
import ActionEditItem from "@/components/actions/ActionEditItem.vue";
import ActionPrintOrder from "@/components/actions/ActionPrintOrder.vue";
import ActionDownloadOrderJobticket from "@/components/actions/ActionDownloadOrderJobticket.vue";
import { useOrdersStore } from "@/store/orders";
import { useUsersStore } from "@/store/users";
import { useFilesStore } from "@/store/files";
import { usePlantsStore } from "@/store/plants";
import { useNotificationStore } from "@base-components/BaseNotifications/notifications";
import useTableColumns from "@/composables/useTableColumns";
import useTableFilters from "@/composables/useTableFilters";
import { formatDate } from "@/utils/formatDate";
import { parse } from "csv/sync";
import { useRecipesStore } from "@/store/recipes";
import useWebSocket from "@/composables/useWebSocket";
import EditableDataTable from "@/components/editableDataTable.vue";
import EditOrderAutoCode from "@/components/EditOrderAutoCode.vue";
import { useProductsStore } from "@/store/products";

export default {
  /**
   * Setup function for the ProductViewOrders component.
   *
   * Initializes and returns reactive variables and functions related to order filters.
   *
   * @returns {Object} An object containing:
   * - `headers`: List of required headers for the table.
   * - `currentFilters`: A reactive object representing the current filters applied to the table.
   * - `activeFilterChips`: An array of active filter chips based on current filters.
   * - `orderStateOptions`: List of order states available for filtering.
   * - `debouncedFetchData`: A debounced function to handle fetching data with updated filters.
   * - `removeFilter`: A function to remove a specific filter from the current filters.
   * - `removeAllFilters`: A function to clear all active filters.
   */
  setup() {
    const { headers } = useTableColumns("productOrders", [
      { title: "Order ID", key: "id" },
      { title: "Plant name", key: "plant_name" },
      { title: "Printer name", key: "dpu_name" },
      { title: "Order Number", key: "order_number" },
      { title: "Owner Name", key: "owner_name" },
      { title: "Papertype", key: "recipe_name" },
      { title: "Created at", key: "created" },
      { title: "State", key: "state" },
    ]);

    const currentFilters = reactive({
      orderId: {
        value: "",
        type: "input",
        label: "Order ID",
      },
      plantNames: {
        value: [],
        type: "multiSelect",
        label: "Plant name",
      },
      printerNames: {
        value: [],
        type: "multiSelect",
        label: "Printer name",
      },
      customerOrderId: {
        value: "",
        type: "input",
        label: "Order Number",
      },
      ownerName: {
        value: "",
        type: "input",
        label: "Owner Name",
      },
      createdBefore: {
        value: null,
        type: "dateInput",
        label: "Created before",
      },
      createdAfter: {
        value: null,
        type: "dateInput",
        label: "Created after",
      },
      state: {
        value: [],
        type: "multiSelect",
        label: "State",
      },
      papertype: {
        value: [],
        type: "multiSelect",
        label: "Papertype",
      },
    });

    const {
      activeFilterChips,
      orderStateOptions,
      removeFilter,
      removeAllFilters,
    } = useTableFilters(currentFilters);

    return {
      headers,
      currentFilters,
      activeFilterChips,
      orderStateOptions,
      removeFilter,
      removeAllFilters,
    };
  },

  components: {
    DataTable,
    FileUploader,
    BaseTable,
    BaseButton,
    BaseInput,
    BaseAutocomplete,
    BaseDateInput,
    FormTimeline,
    ActionCreateItem,
    ActionViewItemInfo,
    ActionDeleteItem,
    ActionEditItem,
    ActionPrintOrder,
    ActionDownloadOrderJobticket,
    EditableDataTable,
    EditOrderAutoCode,
  },

  props: {
    orderCanBeCreated: {
      type: Boolean,
      default: false,
    },
    vardataPresent: {
      type: Boolean,
    },
    isVardataDownloading: {
      type: Boolean,
    },
    selectedProductId: {
      type: String,
    },
    selectedProduct: {
      type: Object,
    },
    uploadedFiles: {
      type: Array,
    },
    vardata: {
      type: Blob,
    },
  },

  emits: ["downloadFile", "deleteFile", "downloadOrder", "updateOrders"],

  /**
   * Data of the component.
   *
   * @typedef {Object} Data
   * @property {Object} config - Configuration object for the project
   * @property {boolean} isSomeModalOpened - Flag indicating if a modal is opened
   * @property {number} currentPage - Current page number
   * @property {number} currentPageSize - Number of items per page
   * @property {number} ordersTotal - Total count of product orders
   * @property {OrdersStore} useOrdersStore - Orders store
   * @property {UsersStore} useUsersStore - Users store
   * @property {FilesStore} useFilesStore - Files store
   * @property {PlantsStore} usePlantsStore - Plants store
   * @property {ProductsStore} useProductsStore - Products store
   * @property {NotificationStore} useNotificationStore - Notifications store
   * @property {Toast} toast - Toast service
   * @property {Object} currentFilters - Current filters for the orders table
   * @property {Array<Object>} sortBy - Sort by configuration
   * @property {Array<Object>} formFields - Form fields configuration
   * @property {Object} ownerName - Object with user name needed for viewing order info
   * @property {Object} ownerEmail - Object with user email needed for viewing order info
   * @property {Array<string>} csvHeaders - CSV headers
   * @property {Array<Object>} csvData - CSV data
   * @property {number} step - Step of the form
   * @property {boolean} isDataValidated - Is data validated
   * @property {File} csvFile - CSV file
   * @property {boolean} isCSVLoaded - Is CSV loaded
   * @property {string | null} selectedPlantId - Selected plant ID
   * @property {boolean} isOrderCreating - Is order creating
   * @property {boolean} areCsvFilesUploading - Are CSV files uploading
   * @property {boolean} isOrderUpdating - Is order updating
   * @property {boolean} isOrderDeleting - Is order deleting
   * @property {boolean} isJobticketDownloading - Flag indicating if the jobticket is being downloaded
   */
  data() {
    return {
      config: config,
      isSomeModalOpened: false,
      currentPage: 1,
      currentPageSize: 10,
      ordersTotal: 10,
      useOrdersStore: useOrdersStore(),
      useRecipesStore: useRecipesStore(),
      useUsersStore: useUsersStore(),
      useFilesStore: useFilesStore(),
      usePlantsStore: usePlantsStore(),
      useProductStore: useProductsStore(),
      useNotificationStore: useNotificationStore(),
      toast: useToast(),
      sortBy: [
        {
          key: "created",
          order: "desc",
        },
      ],
      formFields: [
        {
          value: "",
          initialValue: "",
          options: [],
          type: "text",
          fieldName: "id",
          label: "Order ID",
          required: false,
          visibility: {
            viewAction: true,
            createAction: false,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: this.generateOrderNumber(),
          options: [],
          type: "text",
          fieldName: "order_number",
          label: "Order Number",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxlength: 20,
        },
        {
          value: "",
          initialValue: "",
          options: [],
          type: "select",
          fieldName: "plant_id",
          label: "Plant name",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          options: [],
          type: "select",
          fieldName: "dpu_id",
          label: "Printer name",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          options: [],
          type: "select",
          fieldName: "recipe_id",
          label: "Papertype",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
        },
        {
          value: [],
          initialValue: 1,
          options: [],
          type: "number",
          fieldName: "quantity",
          label: "Quantity",
          required: false,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
        },
        {
          value: [],
          initialValue: [],
          options: [],
          type: "editableDataTable",
          fieldName: "autocodes",
          label: "Auto Codes",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
        },
      ],
      paperType: {
        value: "", // will be set dynamically when the order will be selected
        type: "text",
        label: "Papertype",
        visibility: {
          viewAction: true,
          createAction: false,
          editAction: false,
        },
      },
      ownerName: {
        value: "", // will be set dynamically when the order will be selected
        type: "text",
        label: "Owner name",
        visibility: {
          viewAction: true,
          createAction: false,
          editAction: false,
        },
      },
      ownerEmail: {
        value: "", // will be set dynamically when the order will be selected
        type: "text",
        label: "Owner email",
        visibility: {
          viewAction: true,
          createAction: false,
          editAction: false,
        },
      },
      csvHeaders: [],
      csvData: [],
      step: 1,
      isDataValidated: false,
      csvFile: null,
      isCSVLoaded: false,
      selectedPlantId: null,
      isOrderCreating: false,
      areCsvFilesUploading: false,
      isOrderUpdating: false,
      isOrderDeleting: false,
      isOrderPrinting: false,
      isJobticketDownloading: false,
    };
  },

  computed: {
    /**
     * Returns an array of states in which an order can be deleted.
     * The returned array contains the following states:
     * - READY
     * - ERROR_PROCESSING
     * - ERROR_SENDING_PLANT
     * - ERROR_PCC
     * @returns {string[]}
     */
    deletableStates() {
      return ["READY", "ERROR_PROCESSING", "ERROR_SENDING_PLANT", "ERROR_PCC"];
    },
    hasAutoCodes() {
      if (
        useProductsStore().autoCodes &&
        useProductsStore().autoCodes.length > 0
      ) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * Returns a string indicating the status of creating an order.
     * If this.step > 1, it returns "Order created!".
     * Otherwise, it returns "Create order".
     */
    createOrderStatus() {
      return this.step > 1 ? "Order created!" : "Create order";
    },

    /**
     * Returns a string indicating the status of uploading CSV data.
     * If this.step > 2, it returns "CSV uploaded!".
     * Otherwise, it returns "Upload CSV".
     */
    uploadVardataStatus() {
      return this.step > 2 ? "CSV uploaded!" : "Upload CSV";
    },

    /**
     * Computes and returns a formatted list of product orders from the orders store.
     * Each order's creation date is formatted using the formatDate utility function.
     *
     * @returns {Array<Object>} A list of orders with formatted creation dates.
     */
    orders() {
      return this.useOrdersStore.productOrders.map((order) => {
        if (order.created) {
          order.created = formatDate(order.created);
        }
        return order;
      });
    },

    /**
     * Computes and returns a list of plant IDs and names from the plants store.
     * Each plant is represented as an object with two properties: value (the plant ID) and text (the plant name).
     * This list is used to populate the base select component inside the create order form.
     *
     * @returns {Array<Object>} A list of plant IDs and names.
     */
    plantsNames() {
      return this.usePlantsStore.plantsNames;
    },

    /**
     * Constructs and returns an array of query parameters for fetching product orders.
     * The parameters include sorting, pagination, and page size options.
     * Also in includes filters if there are some chosen.
     *
     * @returns {Array} An array of query parameters as strings.
     */
    requestParams() {
      // We need `view=1` param to get endpoint with 'dpu_name'
      const resultArr = ["view=1"];

      const sortKeyValue = this.sortBy[0].key;
      const sortOrderValue = this.sortBy[0].order;
      if (sortKeyValue && sortOrderValue) {
        const sortOrderSign = sortOrderValue === "asc" ? "+" : "-";
        resultArr.push(`sort=${sortOrderSign}${sortKeyValue}`);
      }

      resultArr.push(`page=${this.currentPage}`);
      resultArr.push(`size=${this.currentPageSize}`);

      const filtersParams = [];

      const filterOrderId = this.currentFilters.orderId.value;
      const filterPlantNames = this.currentFilters.plantNames.value;
      const filterPrinterNames = this.currentFilters.printerNames.value;
      const filterOwnerName = this.currentFilters.ownerName.value;
      const filterCustomerOrderId = this.currentFilters.customerOrderId.value;
      const filterCreatedBefore = this.currentFilters.createdBefore.value;
      const filterCreatedAfter = this.currentFilters.createdAfter.value;
      const filterStates = this.currentFilters.state.value;
      const filterPapertypes = this.currentFilters.papertype.value;

      if (filterOrderId) {
        const parsedFilterOrderId = Number(filterOrderId) || -1;
        filtersParams.push(`id=${parsedFilterOrderId}`);
      }
      if (filterPlantNames.length) {
        filtersParams.push(`plant_id=${filterPlantNames.join("|")}`);
      }
      if (filterPrinterNames.length) {
        filtersParams.push(`dpu_id=${filterPrinterNames.join("|")}`);
      }
      if (filterCustomerOrderId) {
        filtersParams.push(`order_number~=${filterCustomerOrderId}`);
      }
      if (filterOwnerName) {
        filtersParams.push(`owner_name~=${filterOwnerName}`);
      }
      if (filterCreatedBefore) {
        filtersParams.push(
          `created<=${new Date(filterCreatedBefore).toISOString()}`
        );
      }
      if (filterCreatedAfter) {
        filtersParams.push(
          `created>=${new Date(filterCreatedAfter).toISOString()}`
        );
      }
      if (filterStates.length) {
        filtersParams.push(`state=${filterStates.join("|")}`);
      }
      if (filterPapertypes.length) {
        filtersParams.push(`recipe_id=${filterPapertypes.join("|")}`);
      }

      if (filtersParams.length) {
        resultArr.push(`filter=${filtersParams.join(",")}`);
      }

      return resultArr;
    },
  },

  methods: {
    generateOrderNumber() {
      const result = "xxxx-xxxx-xxxx-xxxx".replace(/x/g, function () {
        return Math.floor(Math.random() * 16).toString(16);
      });
      return result;
    },
    /**
     * Handles the opening of any modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleModalOpening() {
      this.isSomeModalOpened = true;
    },

    /**
     * Handles the opening of create order modal on the page by setting the isSomeModalOpened flag to true.
     * Initial values are also set in the form, which are taken from the current product.
     */
    handleCreateModalOpening() {
      const product = this.selectedProduct;

      const plantIdField = this.formFields.find(
        (item) => item.fieldName === "plant_id"
      );

      plantIdField.initialValue = product.plant_id;

      this.usePlantsStore
        .loadPlantDpusNames(product.plant_id)
        .then((plantDpusNames) => {
          const dpuIdField = this.formFields.find(
            (formFieldItem) => formFieldItem.fieldName === "dpu_id"
          );

          dpuIdField.options = plantDpusNames;

          dpuIdField.initialValue = product.dpu_id;

          this.usePlantsStore
            .loadDpuRecipesNames(product.dpu_id)
            .then((dpuRecipesNames) => {
              const recipeIdField = this.formFields.find(
                (formFieldItem) => formFieldItem.fieldName === "recipe_id"
              );

              recipeIdField.options = dpuRecipesNames;

              recipeIdField.initialValue = product.recipe_id;
            });
        });

      this.isSomeModalOpened = true;
    },

    /**
     * Handles the opening of edit order modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleEditModalOpening() {
      const product = this.selectedProduct;

      this.usePlantsStore
        .loadDpuRecipesNames(product.dpu_id)
        .then((dpuRecipesNames) => {
          const recipeIdField = this.formFields.find(
            (formFieldItem) => formFieldItem.fieldName === "recipe_id"
          );

          recipeIdField.options = dpuRecipesNames;

          recipeIdField.initialValue = product.recipe_id;
        });

      this.isSomeModalOpened = true;
    },

    /**
     * Handles the closing of any modal on the page by setting the isSomeModalOpened flag to false
     * and resetting the formData.
     */
    handleModalClosing() {
      this.isSomeModalOpened = false;

      this.resetData();
    },

    /**
     * Checks if a base select input should be disabled.
     *
     * This function is used to conditionally disable base select inputs in the create order form.
     * It takes two parameters: the current item being evaluated and the form data.
     * The function checks if the current item's fieldName is either "dpu_id" or "recipe_id".
     * If it is, it checks if the corresponding plant ID or DPU ID has been selected.
     * If not, it returns true, indicating that the select input should be disabled.
     *
     * @param {Object} item - The current item being evaluated.
     * @param {Array<Object>} formData - The form data containing all the form fields.
     * @returns {boolean} - True if the select input should be disabled, false otherwise.
     */
    isBaseSelectDisabled(item, formData) {
      const plantItem = formData.find((item) => item.fieldName === "plant_id");
      const dpuItem = formData.find((item) => item.fieldName === "dpu_id");

      return (
        (item.fieldName === "dpu_id" && !(plantItem && plantItem.value)) ||
        (item.fieldName === "recipe_id" && !(dpuItem && dpuItem.value))
      );
    },

    /**
     * Determines if the base select input should show a loading indicator.
     *
     * This function checks the following conditions:
     * 1. If the current item's fieldName is "plant_id", it checks if the plants store is loaded.
     * 2. If the current item's fieldName is "dpu_id", it checks if the plant's dpus names are loaded.
     * If any of these conditions are true, it returns true, indicating that the select input should show a loading indicator.
     *
     * @param {Object} item - The current item being evaluated.
     * @param {Array<Object>} formData - The form data containing all the form fields.
     * @returns {boolean} - True if the select input should show a loading indicator, false otherwise.
     */
    isBaseSelectLoading(item, formData) {
      const plantItem = formData.find((item) => item.fieldName === "plant_id");
      const dpuItem = formData.find((item) => item.fieldName === "dpu_id");

      return (
        (item.fieldName === "plant_id" &&
          !this.usePlantsStore.plantsNamesLoaded) ||
        (item.fieldName === "dpu_id" &&
          plantItem &&
          !!plantItem.value &&
          !this.usePlantsStore.plantDpusNamesLoaded[plantItem.value])
      );
    },

    /**
     * Handles the selection of an order and loads the associated CSV file and parses it for preview.
     * It also loads user data and dpus options to show user-friendly label instead of id.
     *
     * @async
     * @param {Object} item - The order item that was selected.
     */
    async selectOrder(item) {
      const userData = await this.useUsersStore.getUserInfo(item.owner_uuid);
      this.ownerName.value = userData.name;
      this.ownerEmail.value = userData.email;

      const order = await this.getOrderVardata(item);
      // We need to load dpus (and recipes) options so that we can show user-friendly label instead of id
      this.usePlantsStore
        .loadPlantDpusNames(item.plant_id)
        .then((plantDpusNames) => {
          this.setDpusNames(plantDpusNames);

          this.usePlantsStore
            .loadDpuRecipesNames(item.dpu_id)
            .then((dpuRecipesNames) => {
              this.setRecipesNames(dpuRecipesNames);
            });
        });

      this.isCSVLoaded = true;

      if (!order) return;

      this.activeOrderId = item.id;

      const csv = await this.readFileAsText(order);
      const parsedCSV = this.parseCSV(csv);

      // Set headers and data for preview
      this.csvHeaders = parsedCSV.headers;
      this.csvData = this.parseRowsForPreview(parsedCSV.rows);
    },

    /**
     * Downloads the jobticket and layout for a specified order as a zip file.
     *
     * This function retrieves the jobticket and layout files from S3 storage for a given order.
     * If the jobticket is not available, a warning is displayed, and the process is terminated.
     * Once both files are obtained, they are packaged into a zip file and downloaded. The function
     * also handles UI elements such as indicating the downloading state and optionally closing a modal.
     *
     * @async
     * @param {Object} order - The order for which the jobticket and layout should be downloaded.
     * @param {Function} closeModal - An optional function to close the modal after the download is completed.
     */
    async downloadJobticket(order, closeModal) {
      this.isJobticketDownloading = true;

      const jobticket = await this.useFilesStore.getFileFromS3(
        this.selectedProductId,
        "jobticket",
        {
          orderId: order.id,
        }
      );

      if (!jobticket) {
        this.toast.warning(
          "No jobticket for download! You need to print an order first",
          { timeout: 5000 }
        );
        return;
      }

      const print = await this.useFilesStore.getFileFromS3(
        this.selectedProductId,
        "print"
      );

      await this.createAndDownloadZip(
        [jobticket, print],
        ["jobticket.json", "print.pdf"],
        `${this.selectedProductId}-${order.id}.zip`
      );

      this.isJobticketDownloading = false;

      if (closeModal) {
        closeModal();
      }
    },

    /**
     * Checks for the presence of vardata for an order and initiates the print process if available.
     *
     * This asynchronous function determines if vardata is present for a given order item.
     * If vardata is not present, it directly attempts to print the order. If vardata is present,
     * it checks for its presence via `getOrderVardata`. If confirmed, it prints the order; otherwise,
     * a warning toast is displayed. The function manages the UI state by indicating the order printing
     * status. Upon successful printing, it closes the modal and refreshes the orders table.
     *
     * @async
     * @param {Object} item - The order item to check for vardata presence.
     * @param {Function} closeModal - Function to close the modal after successful printing.
     */
    async checkVardataPresence(item, closeModal) {
      let result = null;
      this.isOrderPrinting = true;

      if (!this.vardataPresent) {
        result = await this.useOrdersStore.printOrder(item);
        this.isOrderPrinting = false;
      } else {
        const isOrderVardataPresent = await this.getOrderVardata(item);

        if (isOrderVardataPresent) {
          result = await this.useOrdersStore.printOrder(item);
        } else {
          this.toast.warning("No vardata present for a selected order!", {
            timeout: 3000,
          });
        }
      }

      this.isOrderPrinting = false;

      if (result) {
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the orders table with active filters & sorting after action
        await this.handleFetchingOrders();

        const updatedOrder = this.useOrdersStore.productOrders.find(
          (order) => order.order_number === item.order_number
        );

        // Remove notification when order doesn't include "ERROR" in state

        if (
          updatedOrder &&
          updatedOrder.state &&
          !updatedOrder.state.startsWith("ERROR") &&
          !updatedOrder.state.includes("ERROR")
        ) {
          this.useNotificationStore.deleteNotificationById(item.order_number);
        }
      }
    },

    /**
     * Retrieves vardata from S3 for a given order item.
     *
     * @async
     * @param {Object} item - The order item to retrieve vardata for.
     * @returns {Promise<boolean | Object>} A promise that resolves to false if vardata
     * is not present, or the vardata object if it is.
     */
    async getOrderVardata(item) {
      const params = { orderId: item.id };
      const data = await this.useFilesStore.getFileFromS3(
        this.selectedProductId,
        "vardata",
        params
      );

      if (!data) {
        return false;
      } else {
        return data;
      }
    },

    /**
     * Uploads the CSV file to S3.
     *
     * @async
     * @param {Function} closeModal - The function to be called to close the modal
     * after a successful upload.
     */
    async uploadCSVFile(closeModal) {
      if (this.csvFile) {
        this.areCsvFilesUploading = true;

        await this.useFilesStore.uploadFiles(
          [this.csvFile],
          this.selectedProductId,
          "vardata",
          { orderId: this.activeOrderId }
        );
      }

      this.csvFile = null; // Clear the file after upload
      this.areCsvFilesUploading = false;

      if (closeModal) {
        closeModal();
      }
    },

    /**
     * Reads a CSV file and validates it against vardata.
     *
     * @async
     * @param {FileList} filelist - A list of files to read. Only the first file is used.
     */
    async readCSVFile(filelist) {
      const file = filelist[0];

      if (!file) return;

      try {
        const csvData = await this.readFileAsText(file);
        const parsedCSV = this.parseCSV(csvData);

        // Set headers and data for preview
        this.csvHeaders = parsedCSV.headers;
        this.csvData = this.parseRowsForPreview(parsedCSV.rows);

        if (!this.vardata) return; // should stop here if no vardata is present

        const vardataText = await this.readFileAsText(this.vardata);
        const parsedVardata = this.parseCSV(vardataText);

        const validationResult = this.validateCSV(
          parsedCSV,
          parsedVardata.headers
        );

        if (validationResult) {
          this.isDataValidated = true;
          this.csvFile = filelist[0];
        }
      } catch (error) {
        this.toast.warning(`Error reading files: ${error.message}`, {
          timeout: 5000,
        });
      }
    },

    /**
     * Takes a parsed CSV string and returns an array of arrays, where each inner
     * array is a row in the CSV file, and the elements of the inner array are the
     * columns of the CSV file in the order that they appear in the CSV file.
     *
     * @param {Object[][]} csvString - A parsed CSV string where each element is
     * an object with column names as keys and column values as values.
     * @returns {string[][]} - An array of arrays where each inner array is a row
     * in the CSV file, and the elements of the inner array are the columns of the
     * CSV file in the order that they appear in the CSV file.
     */
    parseRowsForPreview(rows) {
      return rows.map((row) => Object.values(row));
    },

    /**
     * Reads a file as text and returns a promise that resolves to the text
     * contents of the file.
     *
     * @param {File} file - The file to read.
     * @returns {Promise<string>} A promise that resolves to the text contents of
     * the file.
     */
    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (event) => {
          if (typeof event.target.result === "string") {
            resolve(event.target.result);
          } else {
            reject(new Error("Expected string content"));
          }
        };

        reader.onerror = (error) => reject(error);

        reader.readAsText(file);
      });
    },

    /**
     * Parses a CSV string and returns an object with headers and rows.
     *
     * @param {string} csvData - The CSV string to parse.
     * @returns {{ headers: string[], rows: { [key: string]: string }[] }} - An
     * object where the `headers` property is an array of the column headers in
     * the CSV file, and the `rows` property is an array of objects, where each
     * object has the column headers as keys and the column values as values.
     */
    parseCSV(csvData) {
      try {
        // Detect delimiter by checking the first line
        const firstLine = csvData.split("\n")[0];
        const delimiter = firstLine.includes(";") ? ";" : ",";

        const records = parse(csvData, {
          columns: true, // Automatically use the first row as headers
          skip_empty_lines: true, // Ignore empty lines to prevent parsing errors
          trim: true, // Remove leading and trailing whitespace from headers and fields
          delimiter: delimiter, // Set the detected delimiter
        });

        // Check if records is empty (only headers, no data)
        if (records.length === 0) {
          // Get headers from the parser's info object
          const headers =
            parse(csvData, {
              columns: false,
              skip_empty_lines: true,
              trim: true,
              delimiter: delimiter,
            })[0] || [];

          return {
            headers,
            rows: [],
          };
        }

        if (!records.length) {
          throw new Error("No data in CSV file");
        }

        const headers = Object.keys(records[0]);
        return { headers, rows: records };
      } catch (error) {
        throw new Error(`Error parsing CSV: ${error.message}`);
      }
    },

    /**
     * Validates a parsed CSV object against the expected headers. If the CSV is
     * invalid, a warning toast is displayed with the details of the invalidation.
     * If the CSV is valid, a success toast is displayed.
     *
     * @param {object} parsedCSV - The parsed CSV object with `headers` and `rows`
     * properties.
     * @param {string[]} expectedHeaders - The expected headers of the CSV file.
     * @returns {boolean} - `true` if the CSV is valid, `false` otherwise.
     */
    validateCSV(parsedCSV, expectedHeaders) {
      const { headers, rows } = parsedCSV;

      const missingHeaders = expectedHeaders.filter(
        (header) => !headers.includes(header)
      );
      const extraHeaders = headers.filter(
        (header) => !expectedHeaders.includes(header)
      );

      if (missingHeaders.length > 0 || extraHeaders.length > 0) {
        let message = "Headers do not match the expected headers!";
        if (missingHeaders.length > 0) {
          message += `\nMissing headers: ${missingHeaders.join(", ")}`;
        }
        if (extraHeaders.length > 0) {
          message += `\nExtra headers: ${extraHeaders.join(", ")}`;
        }
        this.toast.warning(message, { timeout: 5000 });
        return false;
      }

      if (!rows.length) {
        this.toast.warning(
          `CSV file is invalid. There are no any data in rows!`,
          { timeout: 5000 }
        );
        return false;
      }

      const missingDataDetails = rows.reduce(
        (acc, row, index) => {
          const missingHeaders = expectedHeaders.filter(
            (header) => !row[header]
          );
          if (missingHeaders.length > 0) {
            missingHeaders.forEach((header) => {
              acc.details.push(
                `Missing value for header ${header} in row number ${index + 1}`
              );
            });
            acc.missingDataRows.push(row);
          }
          return acc;
        },
        { details: [], missingDataRows: [] }
      );

      if (missingDataDetails.missingDataRows.length > 0) {
        this.toast.warning(
          `CSV file is invalid. Missing data in rows! Details:\n${missingDataDetails.details.join(
            "\n"
          )}`,
          { timeout: 5000 }
        );
        return false;
      }

      this.toast.success("Validation successful!", { timeout: 2000 });
      return true;
    },

    /**
     * Creates a new order and updates the orders table.
     *
     * @param {Object} item - The order data to create.
     * @param {Function} closeModal - The function to close the modal after creating the order.
     *
     * @return {Promise<boolean>} - A promise that resolves to true if the order was created successfully
     * or false if there was an error.
     */
    async createOrder(item, closeModal) {
      if (item.autocodes.length === 0) {
        item.autocodes = useProductsStore().autoCodes.map((autoCode) => {
          return {
            id: autoCode.id,
            variableName: autoCode.variable_name,
            incrementStart: autoCode.config.lastIncrementPrinted,
            incrementStep: autoCode.config.incrementStep,
          };
        });
      }
      const formFieldOrderNumber = this.formFields.find(
        (formFieldItem) => formFieldItem.fieldName === "order_number"
      );

      item.order_number = item.order_number
        .trim()
        .slice(0, formFieldOrderNumber.maxlength);

      this.isOrderCreating = true;
      const result = await this.useOrdersStore.createOrder(
        item,
        this.selectedProductId
      );
      this.isOrderCreating = false;

      if (result) {
        if (this.vardataPresent) {
          this.step = this.step + 1;
          this.activeOrderId = result;
        } else {
          // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
          await closeModal();
        }
        formFieldOrderNumber.initialValue = this.generateOrderNumber();

        // Update the orders table with active filters & sorting after action
        await this.handleFetchingOrders();
      }
    },

    /**
     * Updates an order with the given data and calls the closeModal function if the update is successful.
     *
     * This function sets the `isOrderUpdating` flag to true and calls the `updateOrder` method of
     * the `useOrdersStore`. If the update is successful, it calls the provided `closeModal` function, then
     * updates the orders table with active filters & sorting.
     *
     * @async
     * @param {Object} item - The order data to update.
     * @param {Object} data - The new data to update the order with.
     * @param {Function} closeModal - The closeModal function to be called if the update is successful, typically to close a modal.
     */
    async updateOrder(item, data, closeModal) {
      this.isOrderUpdating = true;
      const result = await this.useOrdersStore.updateOrder(item, data);

      if (result) {
        this.activeOrderId = result;

        await this.uploadCSVFile();
        this.isOrderUpdating = false;

        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the orders table with active filters & sorting after action
        await this.handleFetchingOrders();
      } else {
        this.isOrderUpdating = false;
      }

      this.resetData();
    },

    /**
     * Deletes an order with the given item data and updates the orders table.
     *
     * This function sets the `isOrderDeleting` flag to true, deletes the order using the `useOrdersStore`,
     * and sets the flag to false after the deletion is complete. If the deletion is successful, it calls
     * the provided `closeModal` function, then updates the orders table with active filters & sorting.
     *
     * @async
     * @param {Object} item - The order data to delete.
     * @param {Function} closeModal - The function to be called if the deletion is successful (usually modal closing).
     */
    async deleteOrder(item, closeModal) {
      this.isOrderDeleting = true;
      const result = await this.useOrdersStore.deleteOrder(item);
      this.isOrderDeleting = false;

      if (result) {
        //remove notifiation so no zombies left
        this.useNotificationStore.deleteNotificationById(item.order_number);
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the orders table with active filters & sorting after action
        this.handleFetchingOrders();
      }
    },

    /**
     * Resets all data for the component to initial state.
     *
     * Resets:
     * - step to 1
     * - csvData to an empty array
     * - csvHeaders to an empty array
     * - activeOrderId to null
     * - isDataValidated to false
     * - isCSVLoaded to false
     */
    resetData() {
      this.step = 1;
      this.csvData = [];
      this.csvHeaders = [];
      this.activeOrderId = null;
      this.isDataValidated = false;
      this.isCSVLoaded = false;
    },

    /**
     * Clears the file input and resets the component state for CSV data.
     *
     * Resets:
     * - csvData to an empty array
     * - csvHeaders to an empty array
     * - isDataValidated to false
     */
    clearFile() {
      if (this.$refs.fileUploaderRef) {
        this.$refs.fileUploaderRef.clearUploadInput();
      }

      this.csvData = [];
      this.csvHeaders = [];
      this.isDataValidated = false;
    },

    /**
     * Downloads the given blob as a file with the given filename.
     *
     * The method creates a new link element, sets its href to the blob URL, sets the download
     * attribute to the filename, sets the target to "_blank" and calls the click method to
     * download the file. After the file is downloaded, it revokes the blob URL and removes the
     * link element.
     *
     * @param {Blob} blob - The blob to download as a file.
     * @param {string} filename - The filename to use when downloading the file.
     */
    downloadFileByLink(blob, filename) {
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", filename);
      link.setAttribute("target", "_blank");
      link.click();
      URL.revokeObjectURL(link.href);
      link.remove();
    },

    /**
     * Creates a ZIP archive from the given file blobs and downloads it as a file.
     *
     * The method takes an array of file blobs and an array of corresponding filenames, creates a
     * new JSZip instance, adds the blobs to the zip instance with the given filenames, generates
     * the ZIP file blob asynchronously, and downloads the file blob as a ZIP file using the
     * downloadFileByLink method.
     *
     * @async
     * @param {Blob[]} fileBlobs - The file blobs to add to the ZIP archive.
     * @param {string[]} filenames - The filenames to use for the blobs in the ZIP archive.
     * @param {string} zipFilename - The filename to use when downloading the ZIP file.
     */
    async createAndDownloadZip(fileBlobs, filenames, zipFilename) {
      const zip = new JSZip();

      fileBlobs.forEach((blob, index) => {
        zip.file(filenames[index], blob);
      });

      const zipBlob = await zip.generateAsync({ type: "blob" });

      this.downloadFileByLink(zipBlob, zipFilename);
    },

    /**
     * Handles changes in the select input fields by updating the related data.
     *
     * This async function listens for changes in the select input fields and updates
     * the related data based on the field that was changed. If the "plant_id" field is
     * changed, it loads and sets the Dpus names for the selected plant, and recursively
     * calls itself to preload recipes for the first Dpu if available. If the "dpu_id"
     * field is changed, it loads and sets the recipes names for the selected Dpu.
     *
     * @async
     * @param {Object} item - The object containing the changed field and its new value.
     */
    async onSelectChange(item) {
      if (item.fieldName === "plant_id") {
        const plantDpusNames = await this.usePlantsStore.loadPlantDpusNames(
          item.value
        );

        if (!item.value || plantDpusNames.length === 0) {
          this.setDpusNames([]);
          this.setRecipesNames([]);

          const dpuIdField = this.formFields.find(
            (formFieldItem) => formFieldItem.fieldName === "dpu_id"
          );
          if (dpuIdField) dpuIdField.value = null;
          const recipeIdField = this.formFields.find(
            (formFieldItem) => formFieldItem.fieldName === "recipe_id"
          );
          if (recipeIdField) recipeIdField.value = null;
          return;
        }

        this.setDpusNames(plantDpusNames);

        // If some dpu was changed, we need to preload the recipes as well (of first dpu)
        if (plantDpusNames.length) {
          this.onSelectChange({
            fieldName: "dpu_id",
            value: plantDpusNames[0].value,
          });
        } else {
          this.setRecipesNames([]); // We clean 3rd select in case if there are no dpus for 2nd select
        }
      } else if (item.fieldName === "dpu_id") {
        const dpuRecipesNames = await this.usePlantsStore.loadDpuRecipesNames(
          item.value
        );

        if (!item.value || dpuRecipesNames.length === 0) {
          this.setRecipesNames([]);

          const recipeIdField = this.formFields.find(
            (formFieldItem) => formFieldItem.fieldName === "recipe_id"
          );
          if (recipeIdField) recipeIdField.value = null;
          return;
        }

        this.setRecipesNames(dpuRecipesNames);
      }
    },

    /**
     * Updates the Dpus select options and sets the selected value.
     *
     * @param {Object[]} plantDpusNames - The Dpus values for the selected plant ID.
     */
    setDpusNames(plantDpusNames) {
      const dpuIdField = this.formFields.find(
        (formFieldItem) => formFieldItem.fieldName === "dpu_id"
      );

      dpuIdField.options = plantDpusNames;

      const selectedDpuOption = this.formFields.find(
        (item) => item.value === dpuIdField.value
      );
      if (selectedDpuOption) {
        dpuIdField.value = selectedDpuOption.value;
      }
    },

    /**
     * Updates the recipe select options and sets the selected value.
     *
     * @param {Object[]} dpuRecipesNames - The recipe values for the selected Dpu ID.
     */
    setRecipesNames(dpuRecipesNames) {
      const recipeIdField = this.formFields.find(
        (formFieldItem) => formFieldItem.fieldName === "recipe_id"
      );

      recipeIdField.options = dpuRecipesNames;
      const selectedRecipeOption = this.formFields.find(
        (item) => item.value === recipeIdField.value
      );
      if (selectedRecipeOption) {
        recipeIdField.value = selectedRecipeOption.value;
      }

      // recipeIdField.value = dpuRecipesNames.length
      //   ? dpuRecipesNames[0].value
      //   : "";
    },

    /**
     * Handles fetching orders.
     * Fetches the list of orders from the server and updates the total amount of orders.
     * If the modal is opened, then it doesn't fetch the orders.
     *
     * @async
     */
    async handleFetchingOrders() {
      // We don't trigger request if some modal is opened (except creating step 2)
      if (this.isSomeModalOpened && this.step !== 2) {
        return;
      }

      this.ordersTotal = await this.useOrdersStore.fetchOrdersByProductId(
        this.selectedProductId,
        this.requestParams
      );
      await this.usePlantsStore.fetchAllDpusNames();
    },

    /**
     * Updates the options for the product orders table, including page size,
     * current page, and sorting parameters. Triggers a refresh of the
     * orders list if the data is already loaded, and resets the fetch interval.
     *
     * @param {Object} options - Options object containing the updated
     * pagination and sorting parameters.
     */
    updateOrdersOptions(options) {
      this.currentPageSize = options.itemsPerPage;
      this.currentPage = options.page;

      if (options.sortBy[0]) {
        this.sortBy[0].key = options.sortBy[0].key;
        this.sortBy[0].order = options.sortBy[0].order;
      } else {
        this.sortBy[0].key = null;
        this.sortBy[0].order = null;
      }

      this.handleFetchingOrders();
    },
  },

  watch: {
    /**
     * Emits an update event for the orders.
     *
     * This watcher function is triggered when the `orders` array changes. It emits an "updateOrders" event
     * with the new orders data.
     *
     * @param {Array} value - The array of orders to process and emit.
     */
    orders(value) {
      this.$emit("updateOrders", value);
    },

    /**
     * Watches for changes in the plant names.
     *
     * If there are plant IDs available, it updates the options for the plant ID field
     * in the form fields configuration. It also triggers loading of Dpus names for the first
     * plant ID to cache the Dpus names data for efficient access.
     */
    plantsNames() {
      if (this.plantsNames.length) {
        const plantIdField = this.formFields.find(
          (item) => item.fieldName === "plant_id"
        );

        plantIdField.options = this.plantsNames;
      }
    },

    /**
     * Watches for changes in the currentFilters reactive property.
     *
     * Calls the handleFetchingOrders method whenever the currentFilters
     * property changes. The handleFetchingOrders method fetches data for
     * the orders table.
     */
    currentFilters: {
      handler: useDebounceFn(
        function () {
          this.handleFetchingOrders();
        },
        500,
        { maxWait: 5000 }
      ),
      deep: true,
    },

    /**
     * Watches for changes in the selectedProductId reactive property.
     *
     * Calls the handleFetchingOrders method whenever the selectedProductId
     * property changes. The handleFetchingOrders method fetches data for
     * the orders table.
     */
    selectedProductId: {
      handler() {
        this.removeAllFilters();
        this.handleFetchingOrders();
      },
      immediate: true,
    },
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   * Sets up a WebSocket connection and listens for incoming messages.
   */

  mounted() {
    useWebSocket({
      onMessage: (data) => {
        if (data.type === "orders") {
          if (data.action === "update") {
            this.useOrdersStore.updateProductOrderById({ ...data.data });
          } else {
            this.handleFetchingOrders();
          }
        }
      },
    });
  },
};
</script>

<style scoped lang="scss">
.preview {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 1rem;
}
</style>
