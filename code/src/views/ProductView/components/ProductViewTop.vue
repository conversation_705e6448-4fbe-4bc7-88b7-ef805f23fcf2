<template>
  <!-- Grid with buttons for different actions that can be performed on product -->
  <div class="panel">
    <!-- left buttons (navigation) -->
    <div class="panel-btns">
      <BaseButton btnType="primary" @click="goBackToMainPage">
        Back
      </BaseButton>
      <BaseButton
        btnType="primary"
        @click="selectOtherProduct(false)"
        :disabled="isPrevButtonDisabled"
      >
        Prev product
      </BaseButton>
      <BaseButton
        btnType="primary"
        @click="selectOtherProduct(true)"
        :disabled="isNextButtonDisabled"
      >
        Next product
      </BaseButton>
    </div>

    <!-- dropdown menu with buttons (actions) -->
    <v-menu v-model="isActionsMenuOpened">
      <template v-slot:activator="{ props }">
        <BaseButton
          btnType="primary"
          v-bind="props"
          :append-icon="actionsMenuIcon"
        >
          Actions Menu
        </BaseButton>
      </template>
      <v-list class="panel-actions">
        <BaseButton
          :loading="isPrintableInfoDownloading"
          btnType="primary"
          @click="$emit('downloadPrintableInfo')"
        >
          Download printable info
        </BaseButton>
        <BaseButton
          :disabled="!layoutPresent"
          :loading="isOriginLayoutDownloading"
          btnType="primary"
          @click="$emit('downloadOriginLayout')"
        >
          Download origin layout
        </BaseButton>
        <BaseButton
          :disabled="!isDownloadPrintLayoutBtnActive"
          :loading="isPrintLayoutDownloading"
          btnType="primary"
          @click="$emit('downloadPrintLayout')"
        >
          Download print layout
        </BaseButton>
        <BaseButton
          :disabled="!isDownloadPackageLayoutBtnActive"
          :loading="isPackageLayoutDownloading"
          btnType="primary"
          @click="$emit('downloadPackageLayout')"
        >
          Download package layout
        </BaseButton>
        <BaseButton
          :disabled="!vardataPresent"
          :loading="isCsvDownloading"
          btnType="primary"
          @click="$emit('downloadCSV')"
        >
          Download CSV
        </BaseButton>
        <BaseButton
          v-if="!selectedProduct?.approval_link"
          :disabled="!isReadyForProofing"
          :loading="isProofRequesting"
          btnType="primary"
          @click="$emit('initializeProofing')"
        >
          Initialize proof
        </BaseButton>
        <BaseButton
          v-if="selectedProduct?.approval_link"
          :disabled="!selectedProduct?.approval_link"
          btnType="primary"
          :href="selectedProduct?.approval_link"
          target="_blank"
        >
          Open proof
        </BaseButton>
        <BaseButton
          :disabled="!selectedProduct || !isProductStateFitsEditing"
          btnType="primary"
          :href="editorRoute"
        >
          Go to editor
        </BaseButton>
        <BaseButton
          btnType="primary"
          @click="$emit('openDeleteModal')"
        >
          Delete product
        </BaseButton>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import BaseButton from "@/components/common/BaseButton.vue";
import { useProductsStore } from "@/store/products";
import { PRODUCT_STATE } from "@/constants";

export default {
  components: {
    BaseButton,
  },

  props: {
    layoutPresent: {
      type: Boolean,
      default: false,
    },
    vardataPresent: {
      type: Boolean,
      default: false,
    },
    selectedProduct: {
      type: Object,
    },
    isPrintableInfoDownloading: {
      type: Boolean,
    },
    isOriginLayoutDownloading: {
      type: Boolean,
    },
    isPrintLayoutDownloading: {
      type: Boolean,
    },
    isPackageLayoutDownloading: {
      type: Boolean,
    },
    isCsvDownloading: {
      type: Boolean,
    },
    isProofRequesting: {
      type: Boolean,
    },
  },

  emits: [
    "downloadPrintableInfo",
    "downloadOriginLayout",
    "downloadPrintLayout",
    "downloadPackageLayout",
    "downloadCSV",
    "initializeProofing",
    "selectOtherProduct",
    "openDeleteModal"
  ],

  /**
   * Provides data properties for the component.
   *
   * @returns {Object} The data properties object.
   * @property {ProductsStore} useProductsStore - Instance of the products store.
   */
  data() {
    return {
      useProductsStore: useProductsStore(),
      isActionsMenuOpened: false,
    };
  },

  computed: {
    /**
     * Determines the icon for the actions menu button based on its open state.
     *
     * @returns {string} The icon name to be displayed. Returns "mdi-menu-up" if the actions menu is open,
     * otherwise returns "mdi-menu-down".
     */
    actionsMenuIcon() {
      return this.isActionsMenuOpened ? "mdi-menu-up" : "mdi-menu-down";
    },

    /**
     * Calculates the route to the editor by the selected product ID.
     * @returns {string} Editor route.
     */
    editorRoute() {
      return `${import.meta.env.VITE_EDITOR_API_URL}/product/${
        this.selectedProduct?.id
      }`;
    },

    /**
     * Checks if the selected product is in a state where the "Download print layout" button should be active.
     * @returns {boolean} True if the button should be active, false otherwise.
     */
    isDownloadPrintLayoutBtnActive() {
      return [
        PRODUCT_STATE.PROOF_MISSING,
        PRODUCT_STATE.PROOF_INITIALIZED,
        PRODUCT_STATE.PROOFING_CHANGE,
        PRODUCT_STATE.READY,
        PRODUCT_STATE.DONE,
        PRODUCT_STATE.ERROR_PROOF_INITIALIZED,
        PRODUCT_STATE.ERROR_PROOFING_CHANGE,
      ].includes(this.selectedProduct?.state);
    },

    /**
     * Checks if the selected product is in a state where the "Download package layout" button should be active.
     * @returns {boolean} True if the button should be active, false otherwise.
     */
    isDownloadPackageLayoutBtnActive() {
      return [
        PRODUCT_STATE.PRINTMARKS_MISSING,
        PRODUCT_STATE.PROCESSING,
        PRODUCT_STATE.PROOF_MISSING,
        PRODUCT_STATE.PROOF_INITIALIZED,
        PRODUCT_STATE.PROOFING_CHANGE,
        PRODUCT_STATE.READY,
        PRODUCT_STATE.DONE,
        PRODUCT_STATE.ERROR_PROCESSING,
        PRODUCT_STATE.ERROR_PROOF_INITIALIZED,
        PRODUCT_STATE.ERROR_PROOFING_CHANGE,
      ].includes(this.selectedProduct?.state);
    },

    /**
     * Checks if the selected product is in a state where the "Initialize proof" button should be active.
     * @returns {boolean} True if the button should be active, false otherwise.
     */
    isReadyForProofing() {
      if (this.selectedProduct) {
        return (
          this.selectedProduct.state === PRODUCT_STATE.PROOF_MISSING ||
          this.selectedProduct.state === PRODUCT_STATE.PROOFING_CHANGE
        );
      } else {
        return false;
      }
    },

    /**
     * Checks if the selected product is in a state where the "Go to editor" button should be active.
     * @returns {boolean} True if the button should be active, false otherwise.
     */
    isProductStateFitsEditing() {
      return [
        PRODUCT_STATE.PRINTMARKS_MISSING,
        PRODUCT_STATE.PROOF_MISSING,
        PRODUCT_STATE.READY,
        PRODUCT_STATE.ERROR_PROCESSING,
        PRODUCT_STATE.ERROR_PROOF_INITIALIZED,
        PRODUCT_STATE.ERROR_PROOFING_CHANGE,
      ].includes(this.selectedProduct?.state);
    },

    /**
     * Determines if the "Prev product" button should be disabled.
     *
     * This function checks if there are any filtered product IDs. If no product IDs are present,
     * the button is disabled. If product IDs are present, the button is only disabled when the
     * selected product is the first in the list.
     *
     * @returns {boolean} True if the "Prev product" button should be disabled, false otherwise.
     */
    isPrevButtonDisabled() {
      if (!this.useProductsStore.filteredProductIds?.length) {
        return true;
      }

      return this.useProductsStore.selectedProductIndex === 0;
    },

    /**
     * Determines if the "Next product" button should be disabled.
     *
     * This function checks if there are any filtered product IDs. If no product IDs are present,
     * the button is disabled. If product IDs are present, the button is only disabled when the
     * selected product is the last one in the list.
     *
     * @returns {boolean} True if the "Next product" button should be disabled, false otherwise.
     */
    isNextButtonDisabled() {
      if (!this.useProductsStore.filteredProductIds?.length) {
        return true;
      }

      return (
        this.useProductsStore.selectedProductIndex ===
        this.useProductsStore.filteredProductIds.length - 1
      );
    },
  },

  methods: {
    /**
     * Selects another product by its ID and emits "selectOtherProduct" event.
     *
     * This method takes a boolean parameter `isNext` which indicates whether the
     * next or previous product should be selected. The method uses the
     * `filteredProductIds` and `selectedProductIndex` properties from the
     * `useProductsStore` to determine the ID of the product to be selected and
     * emits the "selectOtherProduct" event with the selected product ID.
     *
     * @param {boolean} isNext - Whether the next or previous product should be selected.
     */
    selectOtherProduct(isNext) {
      const otherProductId =
        this.useProductsStore.filteredProductIds[
          this.useProductsStore.selectedProductIndex + (isNext ? 1 : -1)
        ];

      this.$emit("selectOtherProduct", otherProductId);
    },

    /**
     * Navigates the user back to the main page (page with all products).
     */
    goBackToMainPage() {
      this.$router.push({ name: "products" });
    },
  },
};
</script>

<style lang="scss" scoped>
.panel {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 8px;

  &-btns {
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }

  &-actions {
    padding: 8px;
    display: flex;
    flex-direction: column;
    row-gap: 8px;
  }
}
</style>
