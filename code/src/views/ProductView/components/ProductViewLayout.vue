<template>
  <v-sheet class="product-layout-wrapper">
    <!-- Block with loading indicator while layout is loading -->
    <div
      v-if="isLayoutLoadingNeeded && !layoutLoaded"
      class="product-layout-wrapper__content px-4 py-4 info"
    >
      <div class="info__placeholder">
        Loading layout preview
        <v-progress-circular color="white" height="4" indeterminate rounded />
      </div>
    </div>

    <!-- Block with layout preview (when it is loaded) -->
    <div
      v-else-if="isLayoutLoadingNeeded && layoutLoaded"
      class="product-layout-wrapper__content px-4 py-4 info"
    >
      <div class="info__block">
        <!-- Label -->
        <div class="info__header">
          <h2 class="info__title">Layout</h2>
        </div>

        <!-- Layout preview -->
        <div class="info-list">
          <div class="info-list__row">
            <!-- Layout preview image -->
            <div class="info-list__item info-item">
              <span class="info-item__title">Preview</span>

              <span class="info-item__text">
                <div
                  class="info-layout__preview"
                  :class="{
                    hideLoadingIndicator:
                      fullSizeLayoutLoaded || fullSizeLayoutLoadError,
                  }"
                  :style="{
                    backgroundImage: layout.url ? `url(${layout.url})` : '',
                  }"
                  @click="displayFullSizeLayoutLightbox"
                />

                <span v-if="fullSizeLayoutLoaded && !fullSizeLayoutLoadError">
                  <FsLightbox
                    :toggler="lightboxToggler"
                    :sources="[fullSizeLayout.url]"
                    :types="['image']"
                    :disableLocalStorage="true"
                    :exitFullscreenOnClose="true"
                  />
                </span>
              </span>

              <BaseModal v-if="isReuploadEnabled">
                <!-- Button that opens the modal of uploading layout -->
                <template #activator="{ open }">
                  <IconReupload
                    class="reupload-button"
                    @click="open"
                    title="Reupload layout"
                  />
                </template>

                <!-- Modal for uploading layout -->
                <template #content="{ close }">
                  <v-sheet class="selected-item" :width="420">
                    <p class="selected-item__title">Reupload layout</p>

                    <v-divider />

                    <!-- Form for uploading layout with preview (until it approved or declined) -->
                    <v-form v-if="!uploadFinished">
                      <!-- Layout preview -->
                      <LayoutPreview
                        :layoutWidth="layoutWidth"
                        :layoutHeight="layoutHeight"
                        :sizeInKilobytes="sizeInKilobytes"
                        :imageUploaded="imageUploaded"
                        :layoutURL="layoutURL"
                      />

                      <!-- Layout uploader -->
                      <FileUploader
                        ref="fileUploader"
                        :itemId="selectedProductId"
                        :multiple="false"
                        acceptType="application/pdf"
                        :uploadedFiles="uploadedFiles"
                        :isDragareaHidden="imageUploaded"
                        @downloadFile="(id) => console.log('downloadFile', id)"
                        @deleteFile="(id) => console.log('deleteFile', id)"
                        @uploadFiles="
                          (files) => loadLayoutPreview(files, close)
                        "
                      />

                      <!-- Buttons for approving or declining layout -->
                      <div class="form-buttons mt-2">
                        <BaseButton @click="closeModal(close)"
                          >Close
                        </BaseButton>

                        <BaseButton
                          :visible="!!layoutForUpload && !uploadFinished"
                          @click="declineUpload"
                          btnType="error"
                        >
                          Decline
                        </BaseButton>

                        <BaseButton
                          :visible="!uploadFinished"
                          :disabled="!layoutForUpload"
                          :loading="isLayoutUploading"
                          @click="confirmUpload(close)"
                          btnType="primary"
                        >
                          Approve and upload
                        </BaseButton>
                      </div>
                    </v-form>

                    <!-- Label that is shown when upload is finished -->
                    <span v-else>Uploaded !</span>
                  </v-sheet>
                </template>
              </BaseModal>
            </div>

            <!-- Layout dimensions label -->
            <div class="info-list__item info-item">
              <div class="layout-property">
                <span class="info-item__title">Dimensions</span>
                <span class="info-item__text">
                  {{ layoutProperties.width }} in x
                  {{ layoutProperties.height }} in
                </span>
              </div>
              <div class="layout-property" v-if="selectedProduct.ink_coverage">
                <span class="info-item__title">Ink Coverage</span>
                <span class="info-item__text">
                  {{ inkCoverageManipulation }} %
                </span>
              </div>
              <div class="layout-property" v-if="selectedProduct.ink_usage">
                <span class="info-item__title">Ink Usage</span>
                <div class="info-item__color">
                  <div class="info-item__indicator cyan"></div>
                  <span class="info-item__text">
                    {{ inkUsageManipulation(selectedProduct.ink_usage[0]) }}
                    g/sheet
                  </span>
                </div>
                <div class="info-item__color">
                  <div class="info-item__indicator magenta"></div>
                  <span class="info-item__text">
                    {{ inkUsageManipulation(selectedProduct.ink_usage[1]) }}
                    g/sheet
                  </span>
                </div>
                <div class="info-item__color">
                  <div class="info-item__indicator yellow"></div>
                  <span class="info-item__text">
                    {{ inkUsageManipulation(selectedProduct.ink_usage[2]) }}
                    g/sheet
                  </span>
                </div>
                <div class="info-item__color">
                  <div class="info-item__indicator black"></div>
                  <span class="info-item__text">
                    {{ inkUsageManipulation(selectedProduct.ink_usage[3]) }}
                    g/sheet
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Block with info message if layout is missing -->
    <div v-else class="product-layout-wrapper__content px-4 py-4 info">
      <!-- Info message -->
      <p class="text-center">
        {{ noLayoutMessage }}
      </p>

      <!-- Upload layout button (with modal) -->
      <div
        v-if="isLayoutFileMissing"
        class="info__element w-25 d-flex justify-center"
      >
        <BaseModal>
          <!-- Button that opens the modal of uploading layout -->
          <template #activator="{ open }">
            <BaseButton btnType="primary" @click="open">
              Upload layout
            </BaseButton>
          </template>

          <!-- Modal for uploading layout -->
          <template #content="{ close }">
            <v-sheet class="selected-item" :width="420">
              <p class="selected-item__title">Upload layout</p>

              <v-divider />

              <!-- Form for uploading layout with preview (until it approved or declined) -->
              <v-form v-if="!uploadFinished">
                <!-- Layout preview -->
                <LayoutPreview
                  :layoutWidth="layoutWidth"
                  :layoutHeight="layoutHeight"
                  :sizeInKilobytes="sizeInKilobytes"
                  :imageUploaded="imageUploaded"
                  :layoutURL="layoutURL"
                />

                <!-- Layout uploader -->
                <FileUploader
                  ref="fileUploader"
                  :itemId="selectedProductId"
                  :multiple="false"
                  acceptType="application/pdf"
                  :uploadedFiles="uploadedFiles"
                  :isDragareaHidden="imageUploaded"
                  @downloadFile="(id) => console.log('downloadFile', id)"
                  @deleteFile="(id) => console.log('deleteFile', id)"
                  @uploadFiles="(files) => loadLayoutPreview(files, close)"
                />

                <!-- Buttons for approving or declining layout -->
                <div class="form-buttons mt-2">
                  <BaseButton @click="closeModal(close)">Close</BaseButton>

                  <BaseButton
                    :visible="!!layoutForUpload && !uploadFinished"
                    @click="declineUpload"
                    btnType="error"
                  >
                    Decline
                  </BaseButton>

                  <BaseButton
                    :visible="!uploadFinished"
                    :disabled="!layoutForUpload"
                    :loading="isLayoutUploading"
                    @click="confirmUpload(close)"
                    btnType="primary"
                  >
                    Approve and upload
                  </BaseButton>
                </div>
              </v-form>

              <!-- Label that is shown when upload is finished -->
              <span v-else>Uploaded !</span>
            </v-sheet>
          </template>
        </BaseModal>
      </div>
    </div>
  </v-sheet>
</template>

<script>
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";
import FileUploader from "@/components/FileUploader.vue";
import LayoutPreview from "@/components/LayoutPreview.vue";
import PagePicker from "@/components/modals/PagePicker.vue";
import { useFilesStore } from "@/store/files";
import { useProductsStore } from "@/store/products";
import { useDialog } from "@/composables/useDialog";
import useFileConversion from "@/composables/useFileConversion";
import useValuesConversion from "@/composables/useValuesConversion";
import useOriginalDimensions from "@/composables/useOriginalDimensions";
import { PRODUCT_STATE } from "@/constants";
import FsLightbox from "fslightbox-vue";
import IconReupload from "@/components/icons/IconReupload.vue";

export default {
  /**
   * Sets up and returns the necessary functionalities for the component.
   *
   * @returns {Object} An object containing the `processPDFFile` function
   *                   which provides the capability to process PDF files.
   */
  setup() {
    const { processPDFFile, pageCount } = useFileConversion();
    const { open } = useDialog();
    return {
      processPDFFile,
      pageCount,
      open,
    };
  },

  props: {
    selectedProductId: {
      type: String,
    },
    selectedProduct: {
      type: Object,
    },
    selectedProductState: {
      type: String,
    },
    layout: {
      type: Object,
    },
    fullSizeLayout: {
      type: Object,
    },
    layoutLoaded: {
      type: Boolean,
      default: false,
    },
    fullSizeLayoutLoaded: {
      type: Boolean,
      default: false,
    },
    fullSizeLayoutLoadError: {
      type: Boolean,
      default: false,
    },
    uploadedFiles: {
      type: Array,
    },
  },

  components: {
    BaseModal,
    BaseButton,
    FileUploader,
    LayoutPreview,
    FsLightbox,
    IconReupload,
    PagePicker,
  },

  /**
   * Data properties of the component.
   *
   * @property {Object | null} activeImageLayout - Currently active image layout or null if none.
   * @property {Object | null} layoutForUpload - Layout data prepared for upload or null if none.
   * @property {Boolean} imageUploaded - Flag indicating if an image has been uploaded.
   * @property {Number} layoutHeight - Height of the layout in pixels.
   * @property {Number} layoutWidth - Width of the layout in pixels.
   * @property {Boolean} isLayoutUploading - Flag indicating if the layout is currently uploading.
   * @property {Object} useFilesStore - Reference to the files store.
   * @property {Object} useProductsStore - Reference to the products store.
   * @property {Function} useValuesConversion - Function to handle conversion of values.
   * @property {Function} useOriginalDimensions - Function to obtain original dimensions.
   * @property {Object} layoutProperties - Object containing layout properties.
   * @property {Number} layoutProperties.width - Width of the layout in pixels.
   * @property {Number} layoutProperties.height - Height of the layout in pixels.
   * @property {Number} layoutProperties.size - Size of the layout in kilobytes.
   */
  data() {
    return {
      activeImageLayout: null,
      layoutForUpload: null,
      imageUploaded: false,
      layoutHeight: 0,
      layoutWidth: 0,
      isLayoutUploading: false,
      useFilesStore: useFilesStore(),
      useProductsStore: useProductsStore(),
      useValuesConversion: useValuesConversion(),
      useOriginalDimensions: useOriginalDimensions(),
      layoutProperties: {
        width: 0,
        height: 0,
        size: 0,
      },
      lightboxToggler: false,
    };
  },

  computed: {
    /*
     * Returns the ink coverage value of the selected product.
     * If the product has no ink coverage, it returns 0.
     * The value is taken from the last element of the ink_coverage array
     * and is formatted to two decimal places.
     *
     * @return {String} - The ink coverage value formatted to two decimal places.
     */
    inkCoverageManipulation() {
      return this.selectedProduct.ink_coverage
        ? (
            this.selectedProduct.ink_coverage[
              this.selectedProduct.ink_coverage.length - 1
            ] * 100
          ).toFixed(2)
        : 0;
    },

    /**
     * URL of the uploaded layout image or an empty string if none is uploaded.
     *
     * @return {String}
     */
    layoutURL() {
      return this.activeImageLayout ? this.activeImageLayout.url : "";
    },

    /**
     * Returns the size of the uploaded layout in kilobytes or "0" if none is uploaded.
     *
     * @return {String}
     */
    sizeInKilobytes() {
      return this.layoutForUpload
        ? (this.activeImageLayout.size / 1024).toFixed(2)
        : "0";
    },

    /**
     * Returns true if all uploaded files are fully uploaded (progress 100) and
     * false otherwise.
     *
     * @return {Boolean}
     */
    uploadFinished() {
      return !!(
        this.useFilesStore.uploadedFiles.length &&
        this.useFilesStore.uploadedFiles.every((item) => item.progress === 100)
      );
    },

    /**
     * Returns true if the layout is still loading and false otherwise.
     *
     * @return {Boolean}
     */
    isLayoutLoadingNeeded() {
      return [
        "Loading", // <-- When product state is still loading (this is not state from workflow scheme)
        PRODUCT_STATE.PROCESSING,
        PRODUCT_STATE.PROOF_MISSING,
        PRODUCT_STATE.PROOF_INITIALIZED,
        PRODUCT_STATE.PRINTMARKS_MISSING,
        PRODUCT_STATE.PROOFING_CHANGE,
        PRODUCT_STATE.READY,
        PRODUCT_STATE.DONE,
        PRODUCT_STATE.ERROR_PROCESSING,
        PRODUCT_STATE.ERROR_PROOF_INITIALIZED,
        PRODUCT_STATE.ERROR_PROOFING_CHANGE,
      ].includes(this.selectedProductState);
    },

    /**
     * Returns a message that will be shown if there is no layout.
     *
     * The message depends on the current product state.
     *
     * @return {String}
     */
    noLayoutMessage() {
      switch (this.selectedProductState) {
        case PRODUCT_STATE.ERROR_UPLOADING:
          return "There was an error while uploading the layout. Please try again.";
        case PRODUCT_STATE.FILE_PREPROCESSING:
          return "Layout is currently in preprocessing, please wait.";
        case PRODUCT_STATE.FILE_MISSING:
          return "Upload the layout of your product as PDF to start the workflow.";
        case PRODUCT_STATE.PREFLIGHT:
          return "New layout is currently in preflight, please wait.";
        case PRODUCT_STATE.PROOFING_INITIAL:
          return "New layout needs to be approved.";
        case PRODUCT_STATE.ERROR_PREFLIGHT:
          return "Something went wrong during preflight. Please upload a new file to restart the workflow.";
        case PRODUCT_STATE.ERROR_PROOFING_INITIAL:
          return "Something went wrong while approving the new layout. Please upload a new file to restart the workflow.";
        default:
          return `There is something wrong with the product at the moment. Status: ${this.selectedProductState}`;
      }
    },

    /**
     * Returns true if the current product state is "FILE_MISSING", which means
     * that there is no layout file associated with the product yet.
     *
     * @return {Boolean}
     */
    isLayoutFileMissing() {
      return (
        this.selectedProductState === PRODUCT_STATE.FILE_MISSING ||
        this.selectedProductState === PRODUCT_STATE.ERROR_PREFLIGHT ||
        this.selectedProductState === PRODUCT_STATE.ERROR_PROOFING_INITIAL
      );
    },

    /**
     * Returns true if the current product state allows reupload of the layout.
     *
     * @return {Boolean}
     */
    isReuploadEnabled() {
      return (
        this.selectedProductState === PRODUCT_STATE.ERROR_PREFLIGHT ||
        this.selectedProductState === PRODUCT_STATE.ERROR_PROCESSING ||
        this.selectedProductState === PRODUCT_STATE.ERROR_PROOFING_CHANGE ||
        this.selectedProductState === PRODUCT_STATE.ERROR_PROOFING_INITIAL ||
        this.selectedProductState === PRODUCT_STATE.ERROR_PROOF_INITIALIZED ||
        this.selectedProductState === PRODUCT_STATE.FILE_MISSING ||
        this.selectedProductState === PRODUCT_STATE.PRINTMARKS_MISSING ||
        this.selectedProductState === PRODUCT_STATE.PROOF_MISSING ||
        this.selectedProductState === PRODUCT_STATE.READY
      );
    },
  },

  watch: {
    /**
     * Updates the layout properties when the layout changes.
     */
    layout() {
      const originalDimensions =
        this.useOriginalDimensions.getOriginalDimensions();

      this.useValuesConversion.xPixels = originalDimensions.width;
      this.useValuesConversion.yPixels = originalDimensions.height;

      this.layoutProperties = {
        width: this.useValuesConversion.xInches,
        height: this.useValuesConversion.yInches,
        size: +(this.layout.size / 1024).toFixed(2),
      };
    },

    /**
     * Resets the uploaded files when the upload process is finished successfully.
     *
     * @param {Boolean} status - The status of the upload process. If true, the
     * uploaded files will be reset.
     */
    uploadFinished(status) {
      if (status) {
        this.useFilesStore.resetUploadedFiles();
      }
    },
  },

  methods: {
    /**
     * Formats the ink usage value to a string with two significant digits.
     *
     * @param {Number} color - The ink usage value to format.
     * @return {String} - The formatted ink usage value.
     */
    inkUsageManipulation(color) {
      if (color === 0) return "0.00";
      const d = Math.ceil(Math.log10(Math.abs(color)));
      const power = 2 - d;
      const magnitude = Math.pow(10, power);
      return (Math.round(color * magnitude) / magnitude).toString();
    },

    /**
     * Toggles the full-size layout lightbox.
     */
    displayFullSizeLayoutLightbox() {
      if (this.fullSizeLayoutLoaded) {
        this.lightboxToggler = !this.lightboxToggler;
      }
    },

    /**
     * Closes the modal and resets the uploaded files.
     *
     * @param {Function} callback - The function to call to close the modal.
     */
    async closeModal(callback) {
      await callback();

      this.useFilesStore.resetUploadedFiles();

      this.imageUploaded = false;
      this.activeImageLayout = null;
      this.layoutWidth = 0;
      this.layoutHeight = 0;
      this.layoutForUpload = null;
    },

    /**
     * Loads the layout preview for the given file.
     *
     * @async
     * @param {Array<File>} files - The list of files to be uploaded.
     * @param {Function} close - The function to be called to close the modal.
     */
    async loadLayoutPreview(files, close) {
      const file = files[0];
      const pages = await this.pageCount(file);
      const pageCount = pages.length;

      let pickedPage = 1;
      if (pageCount > 1) {
        pickedPage = await this.open(PagePicker, { file, pageCount });
        if (!pickedPage) {
          this.declineUpload();
          this.closeModal(close);
          return;
        }
      }

      this.imageUploaded = true;
      this.activeImageLayout = await this.processPDFFile(
        files[0],
        "product_" + this.selectedProductId,
        this.selectedProduct.product_number,
        pickedPage
      );
      if (this.activeImageLayout) {
        this.useValuesConversion.xPixels = this.activeImageLayout.width;
        this.useValuesConversion.yPixels = this.activeImageLayout.height;

        this.layoutWidth = parseFloat(this.useValuesConversion.xInches);
        this.layoutHeight = parseFloat(this.useValuesConversion.yInches);

        this.layoutForUpload = files;
      } else {
        this.declineUpload();
        this.closeModal(close);
      }
    },

    /**
     * Initiates and handles the layout file upload process.
     *
     * @async
     * @param {Function} callback - The function to be called to close the modal
     * after a successful upload.
     */
    async confirmUpload(callback) {
      this.isLayoutUploading = true;

      // if layout is available , reset product
      await this.useProductsStore.resetProduct(this.selectedProduct);

      const isSucceeded = await this.useFilesStore.uploadFiles(
        this.layoutForUpload,
        this.selectedProductId,
        "layout"
      );
      this.isLayoutUploading = false;

      // If not succeeded, we don't need to do anything (modal is still opened)
      if (!isSucceeded) {
        return;
      }

      // We need to set state on page and LocalStorage because server doesn't change it in time
      this.useProductsStore.updateSelectedProductState(
        PRODUCT_STATE.FILE_PREPROCESSING
      );

      // Closing modal in case of success
      this.closeModal(callback);
    },

    /**
     * Resets the layout properties and uploaded file after user declines the upload.
     *
     * @async
     */
    async declineUpload() {
      this.$refs.fileUploader.reset();
      this.imageUploaded = false;
      this.activeImageLayout = null;
      this.layoutWidth = 0;
      this.layoutHeight = 0;
      this.layoutForUpload = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.product-layout-wrapper {
  min-height: 466px;
  display: flex;
  justify-content: center;
  align-items: center;

  &__content {
    width: 100%;
  }
}

.v-progress-circular {
  margin-left: 1rem;
}

.fslightbox-container {
  background: white;
}

.reupload-button {
  width: 2rem;
  height: 2rem;
  cursor: pointer;
  background-color: #eeeeee;
  border-radius: 25%;
  padding: 0.25rem;
}

.layout-property {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.75rem;
}

.info-item__color {
  display: flex;
  align-items: center;
}
.info-item__indicator {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}
.info-item__indicator.cyan {
  background-color: #00bcd4;
}
.info-item__indicator.magenta {
  background-color: #e91e63;
}
.info-item__indicator.yellow {
  background-color: #ffeb3b;
}
.info-item__indicator.black {
  background-color: #000000;
}
</style>
