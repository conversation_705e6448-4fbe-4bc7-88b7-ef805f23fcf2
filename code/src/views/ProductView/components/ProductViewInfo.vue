<template>
  <!-- Block with info about selected product -->
  <v-sheet class="px-4 py-4 info">
    <div class="info__block">
      <!-- Title with product name -->
      <div class="info__header">
        <h2 class="info__title">
          {{ selectedProduct.name }}
        </h2>

        <BaseModal>
          <!-- Button that opens modal with fields for current product editing -->
          <template #activator="{ open }">
            <BaseButton
              btnType="primary"
              :disabled="selectedProduct.state === 'DONE'"
              @click="open"
            >
              Edit this product
            </BaseButton>
          </template>

          <template #content="{ close }">
            <!-- Content of modal with options for item editing -->
            <v-sheet class="selected-item" :width="420">
              <!-- Title of modal -->
              <p class="selected-item__title">Edit this product</p>

              <v-divider />

              <!-- Form for product editing -->
              <v-form @submit.prevent="updateProduct(close)">
                <!-- Form inputs (can be text input or select input) -->
                <template v-for="item in editFormFields" :key="item.fieldName">
                  <BaseInput
                    v-if="item.type === 'text'"
                    v-model="item.value"
                    :label="item.label"
                    :type="item.type"
                  />

                  <BaseAutocomplete
                    v-if="item.type === 'select'"
                    :multiple="false"
                    v-model="item.value"
                    :isLoading="item.areOptionsLoading"
                    :label="item.label"
                    :options="item.options"
                  />
                </template>

                <!-- Buttons for either updating product or closing modal -->
                <div class="form-buttons mt-2">
                  <BaseButton @click="close">Close</BaseButton>
                  <BaseButton
                    type="submit"
                    btnType="primary"
                    :disabled="isSubmitBtnDisabled"
                    :loading="isProductUpdating"
                  >
                    Submit
                  </BaseButton>
                </div>
              </v-form>
            </v-sheet>
          </template>
        </BaseModal>
      </div>

      <!-- Grid (2x3) with info labels and their values -->
      <div class="info-list">
        <div class="info-list__row">
          <div class="info-list__item info-item">
            <span class="info-item__title">Product ID</span>
            <span class="info-item__text">{{ selectedProduct.id }}</span>
          </div>

          <div class="info-list__item info-item">
            <span class="info-item__title">Customer name</span>
            <span class="info-item__text">
              {{ selectedProduct.brandowner_name }}
            </span>
          </div>

          <div class="info-list__item info-item">
            <span class="info-item__title">Product Number</span>
            <span class="info-item__text">
              {{ selectedProduct.product_number }}
            </span>
          </div>
        </div>

        <div class="info-list__row">
          <div class="info-list__item info-item">
            <div class="info-item__title-wrapper">
              <span class="info-item__title">State</span>
              <v-icon :color="selectedProduct.stateObj.iconColor">
                {{ selectedProduct.stateObj.icon }}
              </v-icon>
            </div>
            <span class="info-item__text">
              {{
                selectedProduct.stateObj
                  ? selectedProduct.stateObj.message
                  : "-"
              }}
            </span>
          </div>

          <div class="info-list__item info-item">
            <span class="info-item__title">Papertype</span>
            <span class="info-item__text">
              {{ selectedRecipeName }}
            </span>
          </div>

          <div class="info-list__item info-item">
            <span class="info-item__title">Created at</span>
            <span class="info-item__text"
              >{{ formatDate(selectedProduct.created) }}
            </span>
          </div>
        </div>

        <v-divider />

        <div class="info-list__row">
          <div class="info-list__item info-list__item_full-width info-item">
            <span class="info-item__title">Owner</span>
            <span v-if="selectedProduct.ownerObj" class="info-item__text">
              {{ selectedProduct.ownerObj.name }}
            </span>
            <span v-else class="info-item__text">(no owner data)</span>
          </div>
        </div>

        <div class="info-list__row">
          <div class="info-list__item info-list__item_full-width info-item">
            <span class="info-item__title">Approver</span>
            <span v-if="selectedProduct.approverObj" class="info-item__text">
              {{ selectedProduct.approverObj.name }}
            </span>
            <span v-else class="info-item__text">(not approved yet)</span>
          </div>
        </div>
      </div>
    </div>
  </v-sheet>
</template>

<script>
import { watch } from "vue";
import { useProductsStore } from "@/store/products";
import { useBrandownersStore } from "@/store/brandowners";
import { useRecipesStore } from "@/store/recipes";
import BaseButton from "@/components/common/BaseButton.vue";
import BaseInput from "@/components/common/BaseInput.vue";
import BaseAutocomplete from "@/components/common/BaseAutocomplete.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import { formatDate } from "@/utils/formatDate";
import { useOrdersStore } from "@/store/orders";
import { useNotificationStore } from "@base-components/BaseNotifications/notifications";
import { ALERT_STATE } from "@base-components/BaseNotifications/models/enums";
import NotificationPanel from "@base-components/BaseNotifications/BaseNotification.vue";

export default {
  components: {
    NotificationPanel,
    BaseButton,
    BaseInput,
    BaseAutocomplete,
    BaseModal,
  },

  props: {
    selectedProduct: {
      type: Object,
    },
    selectedRecipeName:{
      type: String,
    }
  },

  setup() {
    const notificationStore = useNotificationStore();
    const ordersStore = useOrdersStore();

    watch(
      () => ordersStore.productOrders,
      (orders) => {
        orders.forEach((order) => {
          const currentNotificationId = `${order.order_number}_${order.state}`;

          if (notificationStore.getNotificationById(currentNotificationId)) {
            return;
          }

          const previousNotificationId = `${order.order_number}}`;
          const checkNotification =
            notificationStore.getNotificationById(previousNotificationId);
          if (checkNotification) {
            notificationStore.removeNotificationById(checkNotification.id);
          }

          if (order.state.startsWith("ERROR")) {
            const errorMessage =
              order.message + ". Order Number: " + order.order_number;

            notificationStore.addNotificationByValues(
              errorMessage,
              ALERT_STATE.ERROR,
              "product_" + order.product_id,
              currentNotificationId,
              new Date(order.updated),
              true
            );
          }
        });
      },
      { deep: true, immediate: true }
    );
  },

  /**
   * Reactive data for the component.
   * @returns {Object} object with function formatDate
   * @property {Function} formatDate function to format date
   * @property {boolean} isProductUpdating - Is product updating
   * @property {ProductsStore} useProductsStore - Products store
   * @property {BrandownersStore} useBrandownersStore - Brandowners store
   * @property {RecipesStore} useRecipesStore - Recipes store
   * @property {Array<Object>} editFormFields - Fields of the form for editing product
   */
  data() {
    return {
      formatDate: formatDate,
      isProductUpdating: false,
      useProductsStore: useProductsStore(),
      useBrandownersStore: useBrandownersStore(),
      useRecipesStore: useRecipesStore(),
      editFormFields: [
        {
          value: "",
          type: "text",
          fieldName: "name",
          label: "Name",
        },
        {
          value: null,
          options: [],
          areOptionsLoading: true,
          type: "select",
          fieldName: "brandowner_id",
          label: "Customer",
        },
        {
          value: "",
          type: "text",
          fieldName: "product_number",
          label: "Product Number",
        },
        {
          value: null,
          options: [],
          areOptionsLoading: true,
          type: "select",
          fieldName: "recipe_id",
          label: "Papertype",
        },
      ],
    };
  },

  computed: {
    /**
     * Checks if all the fields in the edit form are filled in.
     * If at least one field is empty, the submit button is disabled.
     * @returns {boolean} - True if all the fields are filled in, false otherwise.
     */
    isSubmitBtnDisabled() {
      return this.editFormFields.some((formField) => !formField.value);
    },
  },

  methods: {
    /**
     * Updates the product with new data and closes the modal with edit form.
     * This function reduces the `editFormFields` array to an object with the new values.
     * It sets the `isProductUpdating` flag to true and calls the `updateProduct` method of
     * the `useProductsStore` with the `selectedProduct` and the new data.
     * If the update is successful, it updates the product details page with new data
     * (calls the `selectProduct` method of the `useProductsStore`) and closes the modal.
     *
     * @async
     * @param {Function} closeModal - The function that closes the modal with edit form
     */
    async updateProduct(closeModal) {
      const data = this.editFormFields.reduce(
        (acc, nextFormField) => {
          acc[nextFormField.fieldName] = nextFormField.value;
          return acc;
        },
        {
          group_id: this.selectedProduct.group_id,
          plant_id: this.selectedProduct.plant_id,
        }
      );

      this.isProductUpdating = true;

      const result = await this.useProductsStore.updateProduct(
        this.selectedProduct,
        data
      );

      if (result) {
        // Update product details page with new data (call the method of the parent component)
        await this.useProductsStore.selectProduct(this.selectedProduct.id);
      }

      this.isProductUpdating = false;

      // Close modal when product was updated and new data was fetched
      closeModal();
    },
  },

  watch: {
    selectedProduct: {
      /**
       * Updates the `editFormFields` with the current values of the product
       * when the `selectedProduct` is changed.
       *
       * @param {Object} product - The current selected product
       */
      handler(product) {
        this.editFormFields.forEach((formField) => {
          formField.value = product[formField.fieldName];
        });
      },
      immediate: true, // We need that flag to set even the initial value of selected product
    },
  },

  /**
   * Fetches the names of all active brandowners and recipes and sets them as options
   * for the corresponding fields in the edit form.
   */
  mounted() {
    const brandownerField = this.editFormFields.find(
      (formField) => formField.fieldName === "brandowner_id"
    );

    if (!brandownerField) {
      console.error("Could not find brandowner field in edit form!");
      return;
    }

    this.useBrandownersStore
      .fetchAllActiveBrandownersNames()
      .then(() => {
        brandownerField.options = this.useBrandownersStore.brandownersNames;
      })
      .finally(() => {
        brandownerField.areOptionsLoading = false;
      });

    const recipeField = this.editFormFields.find(
      (formField) => formField.fieldName === "recipe_id"
    );

    if (!recipeField) {
      console.error("Could not find recipe field in edit form!");
      return;
    }

    this.useRecipesStore
      .fetchRecipesNames()
      .then(() => {
        recipeField.options = this.useRecipesStore.recipesNames;
      })
      .finally(() => {
        recipeField.areOptionsLoading = false;
      });
  },
};
</script>
