<template>
  <!-- Page with all info about selected product -->
  <div class="wrapper">
    <!-- Top buttons (actions) -->
    <ProductViewTop
      :layoutPresent="layoutPresent"
      :vardataPresent="isVardataPresent"
      :selectedProduct="selectedProduct"
      :isPrintableInfoDownloading="isPrintableInfoDownloading"
      :isOriginLayoutDownloading="isOriginLayoutDownloading"
      :isPrintLayoutDownloading="isPrintLayoutDownloading"
      :isPackageLayoutDownloading="isPackageLayoutDownloading"
      :isCsvDownloading="isCsvDownloading"
      :isProofRequesting="isProofRequesting"
      @downloadPrintableInfo="downloadPrintableInfo"
      @downloadOriginLayout="downloadOriginLayout"
      @downloadPrintLayout="downloadPrintLayout"
      @downloadPackageLayout="downloadPackageLayout"
      @downloadCSV="downloadCSV"
      @initializeProofing="initializeProofing"
      @selectOtherProduct="selectOtherProduct"
      @openDeleteModal="openDeleteModal"
    />

    <NotificationPanel :source-item="'product_' + selectedProductId" />

    <!--Modal for product removal -->
    <BaseModal ref="deleteProductRef">
      <template #content="{ close }">
        <v-sheet class="item-action-modal">
          <p class="item-action-modal__message">
            Are you sure you want to delete the product?
          </p>

          <div class="form-buttons">
            <BaseButton @click="close">Close</BaseButton>
            <BaseButton btnType="error" @click="deleteProduct">
              Yes
            </BaseButton>
          </div>
        </v-sheet>
      </template>
    </BaseModal>

    <!-- Product info -->
    <ProductViewInfo
      v-if="selectedProduct"
      :selectedProduct="selectedProduct"
      :selectedRecipeName="selectedRecipeName"
    />

    <!-- Product layout -->
    <ProductViewLayout
      ref="productViewLayout"
      :selectedProductId="selectedProductId"
      :selectedProductState="selectedProductState"
      :selectedProduct="selectedProduct"
      :layout="layout"
      :fullSizeLayout="fullSizeLayout"
      :layoutLoaded="layoutLoaded"
      :fullSizeLayoutLoaded="fullSizeLayoutLoaded"
      :fullSizeLayoutLoadError="fullSizeLayoutLoadError"
      :uploadedFiles="useFilesStore.uploadedFiles"
    />

    <!-- Product orders -->
    <ProductViewOrders
      :uploadedFiles="useFilesStore.uploadedFiles"
      :vardataPresent="isVardataPresent"
      :isVardataDownloading="isVardataDownloading"
      :orderCanBeCreated="orderCanBeCreated"
      :selectedProductId="selectedProductId"
      :selectedProduct="selectedProduct"
      :vardata="vardata"
      @updateOrders="updateOrders"
      @downloadOrder="(orderId) => downloadOrder(orderId)"
    />
  </div>
</template>

<script>
import ProductViewTop from "@/views/ProductView/components/ProductViewTop.vue";
import ProductViewInfo from "@/views/ProductView/components/ProductViewInfo.vue";
import ProductViewLayout from "@/views/ProductView/components/ProductViewLayout.vue";
import ProductViewOrders from "@/views/ProductView/components/ProductViewOrders.vue";
import { useProductsStore } from "@/store/products";
import { useOrdersStore } from "@/store/orders";
import { useFilesStore } from "@/store/files";
import { usePlantsStore } from "@/store/plants";
import useFileConversion from "@/composables/useFileConversion";
import { useToast } from "vue-toastification";
import { PRODUCT_STATE } from "@/constants";
import NotificationPanel from "@base-components/BaseNotifications/BaseNotification.vue";
import { formatDate } from "@/utils/formatDate";
import { preparePrintInfo } from "@/utils/preparePrintInfo";
import useWebSocket from "@/composables/useWebSocket";
import BaseButton from "@base-components/BaseButton.vue";
import BaseModal from "@base-components/BaseModal.vue";
import { useTemplateRef } from "vue";
import { useRecipesStore } from "@/store/recipes";

export default {
  /**
   * Setup function for the product view component.
   *
   * @returns {Object} An object containing the methods and properties used in the component.
   *  {function(Blob): Promise<string>} loadPNGFile - Function to load a PNG file from a Blob.
   *  {function(string): Promise<object>} processPNGFile - Function to process a PNG file.
   *  {import('vue-toastification').UseToastOptions} toast - Toast notification options.
   */
  setup() {
    const { loadPNGFile, processPNGFile } = useFileConversion();
    const toast = useToast();

    return {
      loadPNGFile,
      processPNGFile,
      toast,
    };
  },

  components: {
    BaseModal,
    BaseButton,
    ProductViewTop,
    ProductViewInfo,
    ProductViewLayout,
    ProductViewOrders,
    NotificationPanel,
  },

  /**
   * Data of the component.
   *
   * @typedef {Object} Data
   * @property {ProductsStore} useProductsStore - Products store
   * @property {useOrdersStore} useOrdersStore - Orders store
   * @property {FilesStore} useFilesStore - Files store
   * @property {PlantsStore} usePlantsStore - Plants store
   * @property {RecipesStore} useRecipesStore - Recipes store
   * @property {Object} layout - Layout of the product
   * @property {Object} fullSizeLayout - Full size layout of the product
   * @property {Array<Object>} orders - Orders of the product
   * @property {Blob | null} vardata - Vardata of the product
   * @property {boolean} layoutLoaded - Is layout loaded
   * @property {boolean} isVardataPresent - Is vardata present
   * @property {boolean} isVardataDownloading - Flag indicating if the vardata is being downloaded
   * @property {boolean} isPrintableInfoDownloading - Is printable info downloading
   * @property {boolean} isOriginLayoutDownloading - Is origin layout downloading
   * @property {boolean} isPrintLayoutDownloading - Is print layout downloading
   * @property {boolean} isPackageLayoutDownloading - Is package layout downloading
   * @property {boolean} isCsvDownloading - Is CSV downloading
   * @property {boolean} isProofRequesting - Is proof requesting
   * @property {object | null} deleteProductRef - Reference to the delete modal DOM element, used for programmatic interaction.
   */
  data() {
    return {
      useProductsStore: useProductsStore(),
      useOrdersStore: useOrdersStore(),
      useFilesStore: useFilesStore(),
      usePlantsStore: usePlantsStore(),
      useRecipesStore: useRecipesStore(),
      layout: {},
      fullSizeLayout: {},
      orders: [],
      vardata: null,
      layoutLoaded: false,
      isVardataDownloading: false,
      fullSizeLayoutLoaded: false,
      fullSizeLayoutLoadError: false,
      isVardataPresent: false,
      isPrintableInfoDownloading: false,
      isOriginLayoutDownloading: false,
      isPrintLayoutDownloading: false,
      isPackageLayoutDownloading: false,
      isCsvDownloading: false,
      isProofRequesting: false,
      deleteProductRef: useTemplateRef("deleteProductRef"),
    };
  },

  computed: {
    /**
     * The selected product.
     *
     * @returns {Object} - The selected product.
     */
    selectedProduct() {
      return this.useProductsStore.selectedProduct;
    },

    /**
     * The state of the selected product.
     *
     * @returns {string} - The state of the selected product, or "Loading" if not available.
     */
    selectedProductState() {
      return this.useProductsStore.selectedProduct?.state ?? "Loading";
    },

    /**
     * The ID of the selected product, extracted from the route.
     *
     * @returns { string } - The ID of the selected product.
     */
    selectedProductId() {
      const id = this.$route.params.id;
      return Array.isArray(id) ? id[0] : id;
    },

    /**
     * Takes from store a list of recipes with their IDs as values and names as text,
     * to be used in the product form.
     *
     * @returns {Array} List of recipes in the form of { value: number, text: string }
     */
    recipesNames() {
      return this.useRecipesStore.recipesNames;
    },

    /**
     * Returns the name of the recipe that corresponds to the currently selected product.
     * It matches the `recipe_id` from the selected product with the `value` in the
     * `recipesNames` list, where each recipe is an object of the form { value: string, text: string }.
     *
     */
    selectedRecipeName() {
      const recipe = this.recipesNames.find(
        (recipe) => recipe.value === this.selectedProduct.recipe_id
      );
      return recipe ? recipe.text : "(unknown recipe)";
    },

    /**
     * Checks if the layout object has any properties.
     *
     * @returns {boolean} - True if the layout object contains properties, false otherwise.
     */
    layoutPresent() {
      return Object.keys(this.layout).length > 0;
    },

    /**
     * Determines if an order can be created based on the selected product's state.
     *
     * @returns {boolean} - True if the product state is "READY" or "DONE", false otherwise.
     */
    orderCanBeCreated() {
      if (this.selectedProduct) {
        // It is not possible to create an order because the product state will never be "READY" as of now.
        return (
          this.selectedProduct.state === PRODUCT_STATE.READY ||
          this.selectedProduct.state === PRODUCT_STATE.DONE
        );
      } else {
        return false;
      }
    },

    /**
     * Returns the size of the uploaded layout in kilobytes or "0" if none is uploaded.
     *
     * @returns {String}
     */
    sizeInKilobytes() {
      return this.layoutPresent ? (this.layout.size / 1024).toFixed(2) : "0";
    },
  },

  methods: {
    /**
     * Updates the orders of the product.
     *
     * @param {Array<Object>} orders - The new orders to be set for the product.
     */
    updateOrders(orders) {
      this.orders = orders;
    },

    /**
     * Opens the delete product modal.
     *
     * If the `deleteProductRef` reference exists, it calls its `openModal` method
     * to display the modal for deleting a product.
     *
     */
    openDeleteModal() {
      this.deleteProductRef?.openModal();
    },

    /**
     * Downloads the CSV file for the selected product.
     *
     * @async
     */
    async downloadCSV() {
      this.isCsvDownloading = true;
      const data = await this.getCSV();

      if (data instanceof Blob) {
        const blob = new Blob([data], { type: "text/csv" });
        this.downloadFileByLink(blob, "vardata.csv");
      }

      this.isCsvDownloading = false;
    },

    /**
     * Downloads the CSV file for the given order ID.
     *
     * @async
     * @param {number} orderId - The ID of the order for which the CSV file is to be downloaded.
     */
    async downloadOrder(orderId) {
      this.isVardataDownloading = true;

      const params = { orderId: orderId };
      const data = await this.useFilesStore.getFileFromS3(
        this.selectedProduct.id,
        "vardata",
        params
      );

      if (data instanceof Blob) {
        const blob = new Blob([data], { type: "text/csv" });

        this.downloadFileByLink(blob, `${orderId}.csv`);
      }

      this.isVardataDownloading = false;
    },

    /**
     * Retrieves the CSV file for the selected product.
     *
     * @async
     * @returns {Promise<true | Blob | undefined>} A promise resolving to the CSV file as a Blob if the file is found,
     * or `undefined` if it is not.
     */
    async getCSV() {
      const data = await this.useFilesStore.getFileFromS3(
        this.selectedProduct.id,
        "vardata"
      );

      if (!data) {
        this.toast.warning("No data for download!", { timeout: 1000 });

        return;
      } else {
        return data;
      }
    },

    /**
     * Asynchronously downloads and prints product and order information for the selected product.
     *
     * This function gathers detailed information about the selected product, including its name,
     * ID, brand owner name, product number, state, state icon, recipe name, creation date, owner,
     * and approver information. It also collects layout information and fetches all orders with
     * owners' info for the selected product. The gathered data is formatted and prepared for
     * printing.
     *
     * The function modifies the DOM to insert the prepared print content, temporarily hides
     * the existing body content, and triggers the browser's print functionality. After printing,
     * it restores the body content and removes the print container.
     *
     * The downloading state is tracked with `isPrintableInfoDownloading` to indicate the process.
     *
     * @async
     */
    async downloadPrintableInfo() {
      const productInfo = {
        name: this.selectedProduct.name,
        id: this.selectedProduct.id,
        brandownerName: this.selectedProduct.brandowner_name,
        productNumber: this.selectedProduct.product_number,
        stateName: this.selectedProduct.state,
        stateIcon: this.selectedProduct.stateObj.icon,
        stateIconColor: this.selectedProduct.stateObj.iconColor,
        recipeName: this.selectedRecipeName,
        creationDate: formatDate(this.selectedProduct.created),
        inkCoverage: (
          this.selectedProduct.ink_coverage[
            this.selectedProduct.ink_coverage.length - 1
          ] * 100
        ).toFixed(2),
        inkUsage: {
          cyan:
            "Cyan: " +
            this.selectedProduct.ink_coverage[0].toFixed(2).toString() +
            " g/sheet",
          magenta:
            "Magenta: " +
            this.selectedProduct.ink_coverage[1].toFixed(2).toString() +
            " g/sheet",
          yellow:
            "Yellow: " +
            this.selectedProduct.ink_coverage[2].toFixed(2).toString() +
            " g/sheet",
          black:
            "Black: " +
            this.selectedProduct.ink_coverage[3].toFixed(2).toString() +
            " g/sheet",
        },
      };

      if (this.selectedProduct.ownerObj) {
        productInfo.ownerInfo = {
          name: this.selectedProduct.ownerObj.name,
          email: this.selectedProduct.ownerObj.email,
        };
      }

      if (this.selectedProduct.approverObj) {
        productInfo.approverInfo = {
          name: this.selectedProduct.approverObj.name,
          email: this.selectedProduct.approverObj.email,
        };
      }

      const layoutInfo = {
        width: this.$refs.productViewLayout?.layoutProperties.width ?? "?",
        height: this.$refs.productViewLayout?.layoutProperties.height ?? "?",
        url: this.layout.url,
      };

      // The headers that we want to see in the table while printing
      const tableHeaders = [
        { title: "Order ID", key: "id" },
        { title: "Owner name", key: "ownerName" },
        { title: "Owner email", key: "ownerEmail" },
        { title: "Plant name", key: "plant_name" },
        { title: "Printer name", key: "dpu_name" },
        { title: "Order number", key: "order_number" },
        { title: "Created at", key: "created" },
        { title: "State", key: "state" },
      ];

      this.isPrintableInfoDownloading = true;

      // The array of all the orders (with owners info) for the selected product
      const allProductOrders =
        await this.useOrdersStore.fetchAllOrdersWithOwnersInfoByProductId(
          this.selectedProductId
        );

      // We want to leave only the fields that we need in the table depending on the headers
      const formattedOrders = allProductOrders.map((fullOrderObj) =>
        tableHeaders.reduce(
          (acc, { key }) => ({
            ...acc,
            [key]:
              key === "created"
                ? formatDate(fullOrderObj[key])
                : fullOrderObj[key],
          }),
          {}
        )
      );

      const ordersTableInfo = {
        headers: tableHeaders,
        data: formattedOrders,
      };

      const printContent = preparePrintInfo(
        productInfo,
        layoutInfo,
        ordersTableInfo
      );

      document.body.insertAdjacentHTML("beforebegin", printContent);
      document.body.classList.add("hidden-block");
      window.print();
      document.body.classList.remove("hidden-block");
      document.querySelector(".print-container").remove();

      this.isPrintableInfoDownloading = false;
    },

    /**
     * Downloads the origin layout PDF file for the selected product.
     *
     * If the file is not found, a warning message is displayed. Otherwise, the file is downloaded
     * using the `downloadFileByLink` utility function.
     *
     * @async
     */
    async downloadOriginLayout() {
      this.isOriginLayoutDownloading = true;

      const data = await this.useFilesStore.getFileFromS3(
        this.selectedProduct.id,
        "layout"
      );

      if (!data) {
        this.toast.warning("No origin layout for download!", { timeout: 1000 });
      } else if (data instanceof Blob) {
        this.downloadFileByLink(data, "layout.pdf");
      }

      this.isOriginLayoutDownloading = false;
    },

    /**
     * Downloads the print layout PDF file for the selected product.
     *
     * If the file is not found, a warning message is displayed. Otherwise, the file is downloaded
     * using the `downloadFileByLink` utility function.
     *
     * @async
     */
    async downloadPrintLayout() {
      this.isPrintLayoutDownloading = true;
      const data = await this.useFilesStore.getFileFromS3(
        this.selectedProduct.id,
        "print"
      );

      if (!data) {
        this.toast.warning("No print layout for download!", { timeout: 1000 });
      } else if (data instanceof Blob) {
        this.downloadFileByLink(data, "print_layout.pdf");
      }

      this.isPrintLayoutDownloading = false;
    },

    /**
     * Downloads the preview layout PDF file for the selected product.
     *
     * If the file is not found, a warning message is displayed. Otherwise, the file is downloaded
     * using the `downloadFileByLink` utility function.
     *
     * @async
     */
    async downloadPackageLayout() {
      this.isPackageLayoutDownloading = true;
      const data = await this.useFilesStore.getFileFromS3(
        this.selectedProduct.id,
        "preview"
      );

      if (!data) {
        this.toast.warning("No package layout for download!", {
          timeout: 1000,
        });
      } else if (data instanceof Blob) {
        this.downloadFileByLink(data, "package_layout.pdf");
      }

      this.isPackageLayoutDownloading = false;
    },

    /**
     * Initiates the download of a file represented by a Blob.
     *
     * This function creates an anchor element, sets its href to a URL created
     * from the Blob, and specifies the filename for the download. It simulates
     * a click event on the anchor to trigger the download, then revokes the
     * Blob URL and removes the anchor element from the document.
     *
     * @param {Blob} blob - The Blob object representing the file to download.
     * @param {string} filename - The name to assign to the downloaded file.
     */
    downloadFileByLink(blob, filename) {
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", filename);
      link.setAttribute("target", "_blank");
      link.click();
      URL.revokeObjectURL(link.href);
      link.remove();
    },

    /**
     * Utility function to open and return the application's dedicated image cache.
     *
     * @async
     */
    async getImageCache() {
      return caches.open("image-cache");
    },

    /**
     * Fetches the thumbnail for the selected product and sets the layout
     * accordingly.
     *
     * This method fetches the thumbnail for the selected product from the
     * FilesStore, loads the PNG file from the blob, and processes the PNG
     * file to extract its layout information. It then assigns this layout
     * information to the `layout` property and sets the `layoutLoaded`
     * property to true.
     *
     * Implements a "Stale-While-Revalidate" caching strategy.
     * It serves the cached version immediately (if available) and then
     * updates the cache with the latest version from the network in the background.
     *
     * @async
     */
    async fetchThumbnail() {
      this.layoutLoaded = false;
      const cache = await this.getImageCache();
      const cacheKey = `/product-thumbnail-${this.selectedProductId}.png`;

      // Attempt to get from cache (immediately)
      const cachedResponse = await cache.match(cacheKey);

      if (cachedResponse) {
        const blob = await cachedResponse.blob();
        const pngFile = await this.loadPNGFile(blob);
        this.layout = await this.processPNGFile(pngFile);
        this.layoutLoaded = true; // Displaying a cached image
      }

      // In the background: Requesting a new version from the network and updating the cache
      try {
        const networkBlob = await this.useFilesStore.getThumbnail(
          this.selectedProductId
        );

        if (networkBlob instanceof Blob) {
          // Compare BLOBs to update the UI only if there are changes
          await cache.put(
            cacheKey,
            new Response(networkBlob, {
              headers: { "Content-Type": "image/png" },
            })
          );
          const newPngFile = await this.loadPNGFile(networkBlob);
          const newLayout = await this.processPNGFile(newPngFile);
          this.layout = newLayout;
        } else {
          console.warn(
            `Failed to fetch latest thumbnail for product ${this.selectedProductId}.`
          );
        }
      } catch (error) {
        console.error("Error fetching latest thumbnail from network:", error);
      } finally {
        if (!cachedResponse) {
          this.layoutLoaded = true;
        }
      }
    },

    /**
     * Fetches the full size thumbnail for the selected product and sets the layout
     * accordingly.
     *
     * This method fetches the fetch full size thumbnail for the selected product from the
     * FilesStore, loads the PNG file from the blob. It then assigns this layout
     * information to the `fullSizeLayout` property.
     *
     * Implements a "Stale-While-Revalidate" caching strategy.
     * It serves the cached version immediately (if available) and then
     * updates the cache with the latest version from the network in the background.
     *
     * @async
     */
    async fetchFullSizeThumbnail() {
      this.fullSizeLayoutLoaded = false;
      this.fullSizeLayoutLoadError = false;

      const cache = await this.getImageCache();
      const cacheKey = `/product-fullsize-thumbnail-${this.selectedProductId}.png`;

      // Attempt to get from cache (immediately)
      const cachedResponse = await cache.match(cacheKey);

      if (cachedResponse) {
        const blob = await cachedResponse.blob();
        const pngFile = await this.loadPNGFile(blob);
        this.fullSizeLayout = await this.processPNGFile(pngFile);
        this.fullSizeLayoutLoaded = true; // Displaying a cached image
      }

      // In the background: Requesting a new version from the network and updating the cache
      try {
        const networkBlob = await this.useFilesStore.getFullSizeThumbnail(
          this.selectedProductId
        );
        if (networkBlob instanceof Blob) {
          await cache.put(
            cacheKey,
            new Response(networkBlob, {
              headers: { "Content-Type": "image/png" },
            })
          );
          const newPngFile = await this.loadPNGFile(networkBlob);
          this.fullSizeLayout = await this.processPNGFile(newPngFile);
        } else {
          console.warn(
            `Failed to fetch latest full size thumbnail for product ${this.selectedProductId}.`
          );
          this.fullSizeLayoutLoadError = true;
        }
      } catch (error) {
        console.error(
          "Error fetching latest full size thumbnail from network:",
          error
        );
        this.fullSizeLayoutLoadError = true;
      } finally {
        if (!cachedResponse) {
          this.fullSizeLayoutLoaded = true;
        }
      }
    },

    /**
     * Initializes the proofing process for the selected product.
     *
     * This method sends a request to the server to initialize the proofing
     * process for the selected product. It sets the `isProofRequesting`
     * property to true while the request is being processed, and sets it to
     * false when the request is complete.
     *
     * @async
     */
    async initializeProofing() {
      this.isProofRequesting = true;
      await this.useProductsStore.initializeProductProofing(
        this.selectedProduct
      );
      this.isProofRequesting = false;
    },

    /**
     * Fetches the product data and updates the product state and related layouts.
     *
     * This function checks if the products are loaded; if not, it triggers a fetch
     * operation to load them. It then retrieves the current state of the selected
     * product and compares it with the new state after fetching the product details.
     * If the state changes to a specific set of states, it fetches the thumbnails
     * for the product. Additionally, it fetches the 'vardata' file from S3 and
     * updates the `isVardataPresent` flag accordingly. The function also fetches
     * the plant names associated with the product.
     *
     * @async
     */
    async fetchData() {
      if (!this.useProductsStore.products.length) {
        // If there are no products loaded, we need to fetch them at least once
        await this.useProductsStore.fetchProducts();
      }

      const oldProductState = this.selectedProductState;
      const oldProductId = this.selectedProductId;

      await this.useProductsStore
        .selectProduct(this.selectedProductId)
        .then(() => {
          const newProductState = this.selectedProductState;
          if (
            [
              PRODUCT_STATE.PROCESSING,
              PRODUCT_STATE.PROOF_MISSING,
              PRODUCT_STATE.PROOF_INITIALIZED,
              PRODUCT_STATE.PRINTMARKS_MISSING,
              PRODUCT_STATE.PROOFING_CHANGE,
              PRODUCT_STATE.READY,
              PRODUCT_STATE.DONE,
              PRODUCT_STATE.ERROR_PROCESSING,
              PRODUCT_STATE.ERROR_PROOF_INITIALIZED,
              PRODUCT_STATE.ERROR_PROOFING_CHANGE,
            ].includes(newProductState)
          ) {
            this.fetchThumbnail(); // fetch thumbnail only once, when product state is fetched
            this.fetchFullSizeThumbnail(); // fetch full size thumbnail only once, when product state is fetched
          }
        });

      this.useFilesStore
        .getFileFromS3(this.selectedProductId, "vardata")
        .then((vardata) => {
          if (vardata) {
            this.isVardataPresent = true;
            this.vardata = vardata;
          }
        });

      await this.usePlantsStore.fetchPlantsNames();
    },

    /**
     * Selects another product and updates the view with the selected product's data.
     *
     * This method sets several properties to false, indicating that the
     * corresponding data is not yet loaded. It then pushes a new route to the
     * router with the ID of the selected product. Finally, it calls the
     * `fetchData` method to fetch the product, vardata, and plant information
     * for the selected product.
     *
     * @param {string} otherProductId - The ID of the product to be selected.
     * @async
     */
    async selectOtherProduct(otherProductId) {
      // We need to wait route change in order to change selectedProductId computed property
      await this.$router.push({
        name: "product",
        params: { id: otherProductId },
      });

      this.layout = {};
      this.orders = [];
      this.vardata = null;
      this.layoutLoaded = false;
      this.fullSizeLayoutLoaded = false;
      this.isVardataPresent = false;
      this.isOriginLayoutDownloading = false;
      this.isPrintLayoutDownloading = false;
      this.isPackageLayoutDownloading = false;
      this.isCsvDownloading = false;
      this.isProofRequesting = false;
      this.isProductLoaded = false;

      await this.fetchData();
    },

    /**
     * Deletes the currently selected product using the store action.
     * On successful deletion, navigates the user back to the products page.
     *
     * @async
     */
    async deleteProduct() {
      try {
        const result = await this.useProductsStore.deleteProduct(
          this.selectedProduct
        );
        if (result) {
          this.$router.push("/products");
        }
      } catch (error) {
        this.toast.error("An error occurred during deletion", {
          timeout: 2000,
        });
      }
    },
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   * Sets up a WebSocket connection and listens for incoming messages.
   */

  mounted() {
    useProductsStore().getAutoCodes(this.selectedProductId);
    this.fetchData();
    useWebSocket({
      onMessage: (data) => {
        if (data.type === "products") {
          if (data.action === "update") {
            this.useProductsStore.selectProduct(this.selectedProductId);
            if (
              data.data.ink_coverage &&
              this.selectedProduct.ink_coverage &&
              parseInt(this.selectedProductId) === parseInt(data.data.id) &&
              this.selectedProductState !== data.data.state
            ) {
              useProductsStore().getAutoCodes(this.selectedProductId);
              this.fetchThumbnail();
              this.fetchFullSizeThumbnail();
            }
          }
        }
      },
    });
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>
