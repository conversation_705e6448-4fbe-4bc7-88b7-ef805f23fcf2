<template>
  <!-- Main header of the application -->
  <AppBar :title :leftButtons :rightButtons />
    <v-navigation-drawer :modelValue="isTocOpen">
      <v-list style="overflow-y: auto">
        <v-list-item
          v-for="(folder, index) in folders"
          :key="index"
          style="margin-bottom: 1em"
        >
          <v-list-item
            @click="loadMarkdown(folder.path)"
            density="compact"
            style="font-size: large"
            :class="{ 'active-item': currentFile === folder.path }"
          >
            {{ folder.name }}
          </v-list-item>

          <v-list-item
            density="compact"
            v-for="(subItem, subIndex) in folder.files"
            :key="subIndex"
            @click="loadMarkdown(subItem.path)"
            :class="{ 'active-item': currentFile === subItem.path }"
            style="margin-left: 1em; font-size: 2em"
            class="custom-list-item"
          >
            <v-list-item density="compact">
              <v-list-item-title>{{ subItem.name }}</v-list-item-title>
            </v-list-item>
          </v-list-item>
          <v-divider
            v-if="index <= folders.length - 2 && folder.files.length > 0"
          ></v-divider>
        </v-list-item>
      </v-list>
    </v-navigation-drawer>

    <!-- Content Space -->
    <v-main style="background-color: white; height: 100%">
      <NotificationPanel />
      <transition name="scroll-fade" mode="out-in">
        <vue-markdown
          class="markdown-frame"
          :key="currentFile"
          :source
          :options
        />
      </transition>
    </v-main>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import VueMarkdown from "vue-markdown-render";
import { useWindowSize } from "@vueuse/core";
import AppBar from "@/components/AppBar.vue";
import axios from "axios";
import NotificationPanel from "@/components/common/BaseNotifications/BaseNotification.vue";

// Definiere die Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const source = ref("");
const folders = ref([]);
const isTocOpen = ref(true);
const { width } = useWindowSize();

var locale = "en";
var basePath = locale === "de" ? "/docs/de" : "/docs/en";
var currentFile = ref("");

// Title in App Bar
const title = "Packsuite - Manual";

// Definiere die benutzerdefinierten Schaltflächen
const leftButtons = [
  {
    icon: "mdi-menu",
    callback: () => {
      toggleToc();
    },
  },
];

// Definiere die benutzerdefinierten Schaltflächen
const rightButtons = [];

const toggleToc = () => {
  isTocOpen.value = !isTocOpen.value;
};

const options = {
  breaks: true,
  html: true,
  xhtmlOut: true,
  typographer: true,
  linkify: true,
};

const loadMarkdown = async (path) => {
  try {
    const response = await axios.get(basePath + path);
    if (response.headers["content-type"].includes("text/markdown")) {
      currentFile.value = path;
      source.value = response.data;
    } else {
      console.error("Error on loading the documentation.");
    }
  } catch (error) {
    console.error("Error on fetch markdown", error);
  }
};

const loadFolders = async () => {
  try {
    const response = await axios.get(`/docs/structure.json`);
    if (response.headers["content-type"].includes("application/json")) {
      folders.value = response.data;
    } else {
      console.error("Error on loading structure.json.");
    }
  } catch (error) {
    console.error("There was a problem with the fetch operation:", error);
  }
};

/**
 * Checks if the window width is greater than or equal to 1280px and toggles the Table of Content accordingly.
 * @param {number} windowWidth - The current window width.
 */
const checkTocIfDesktop = (windowWidth) => {
  if (windowWidth >= 1500 && isTocOpen.value === false) {
    toggleToc();
  } else if (windowWidth < 1500 && isTocOpen.value === true) {
    toggleToc();
  }
};

watch(width, () => {
  checkTocIfDesktop(width.value);
});

onMounted(() => {
  loadFolders();
  loadMarkdown("/product/create.md");
});

onMounted(async () => {
  isTocOpen.value = true;
});
</script>

<style lang="scss" scoped>
/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #444444;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: rgb(122, 122, 122);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.custom-list-item {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.v-layout {
  height: 100%;
}

.v-navigation-drawer {
  background-color: #454545 !important;
  color: white;
  border-left: 1px solid black;
}

.v-navigation-drawer__content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.scroll-fade-enter-active,
.scroll-fade-leave-active {
  transition:
    opacity 0.5s,
    transform 0.5s;
}

.scroll-fade-enter,
.scroll-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.scroll-fade-enter-active {
  transition:
    opacity 0.5s,
    transform 0.5s;
}

.scroll-fade-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.scroll-fade-leave-active {
  transition:
    opacity 0.5s,
    transform 0.5s;
}

.scroll-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.active-item {
  background-color: #646464 !important;
  /* Change this to your desired highlight color */
}

.markdown-frame {
  padding: 3rem;
  background-color: white;
  margin: 2rem;
}
</style>
