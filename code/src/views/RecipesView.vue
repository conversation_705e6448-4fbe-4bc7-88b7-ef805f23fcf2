<template>
  <!-- Recipes table -->
  <DataTable
    title="Recipes"
    :sortBy
    :items="recipes"
    :itemsLength="useRecipesStore.recipesTotal"
    :itemsPerPage="currentPageSize"
    :currentPage="currentPage"
    :headers
    :formFields
    :loading="!useRecipesStore.recipesLoaded"
    :hasActiveFilters="!!activeFilterChips.length"
    emptyMsg="No recipes available"
    @removeAllFilters="removeAllFilters"
    @updateOptions="updateRecipesOptions"
  >
    <!-- Chips containing active filters -->
    <template #filtersChipsSlot>
      <v-chip
        class="vuetify-chip-wrapper"
        v-for="filterChip in activeFilterChips"
        :key="filterChip.key"
        closable
        @click:close="
          removeFilter(
            filterChip.filterType,
            filterChip.filterKey,
            filterChip.value
          )
        "
      >
        {{ filterChip.label }}
      </v-chip>
    </template>

    <!-- Filters input fields -->
    <template #filtersInputsSlot>
      <BaseInput
        v-if="config.tablesColumns.recipes.id"
        v-model="currentFilters.id.value"
        :label="currentFilters.id.label"
      />
      <BaseInput
        v-if="config.tablesColumns.recipes.name"
        v-model="currentFilters.name.value"
        :label="currentFilters.name.label"
      />
      <BaseDateInput
        v-if="config.tablesColumns.recipes.created"
        v-model="currentFilters.createdBefore.value"
        :label="currentFilters.createdBefore.label"
      />
      <BaseDateInput
        v-if="config.tablesColumns.recipes.created"
        v-model="currentFilters.createdAfter.value"
        :label="currentFilters.createdAfter.label"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.recipes.disabled"
        v-model="currentFilters.disabled.value"
        :label="currentFilters.disabled.label"
        :options="disabledValuesOptions"
      />
    </template>

    <!-- Actions (view info, delete, edit) -->
    <template
      #actions="{
        item,
        setSelectedItem,
        formData,
        resetFormData,
        parseFormData,
      }"
    >
      <ActionViewItemInfo
        :item="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <ActionDisableRecipe
        :isUpdating="isRecipeStatusUpdating"
        :recipe="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        :parseFormData="parseFormData"
        @updateRecipe="
          (item, data, closeModal) =>
            updateRecipeStatus(item, data.disabled, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <ActionEnableRecipe
        :isUpdating="isRecipeStatusUpdating"
        :recipe="item"
        :setSelectedItem="setSelectedItem"
        :formData="formData"
        :parseFormData="parseFormData"
        @updateRecipe="
          (item, data, closeModal) =>
            updateRecipeStatus(item, data.disabled, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />
    </template>
  </DataTable>
</template>

<script>
import config from "@/assets/config.json";
import { reactive } from "vue";
import { useDebounceFn } from "@vueuse/core";
import DataTable from "@/components/DataTable.vue";
import BaseInput from "@/components/common/BaseInput.vue";
import BaseAutocomplete from "@/components/common/BaseAutocomplete.vue";
import BaseDateInput from "@/components/common/BaseDateInput.vue";
import ActionViewItemInfo from "@/components/actions/ActionViewItemInfo.vue";
import ActionDisableRecipe from "@/components/actions/ActionDisableRecipe.vue";
import ActionEnableRecipe from "@/components/actions/ActionEnableRecipe.vue";
import { useRecipesStore } from "@/store/recipes";
import useTableColumns from "@/composables/useTableColumns";
import useTableFilters from "@/composables/useTableFilters";
import { formatDate } from "@/utils/formatDate";
import useWebSocket from "@/composables/useWebSocket";

export default {
  /**
   * Setup function for the RecipesView component.
   *
   * Initializes and returns reactive variables and functions related to recipes filters.
   *
   * @returns {Object} An object containing:
   * - `headers`: List of required headers for the table.
   * - `currentFilters`: A reactive object representing the current filters applied to the table.
   * - `activeFilterChips`: An array of active filter chips based on current filters.
   * - `debouncedFetchData`: A debounced function to handle fetching data with updated filters.
   * - `removeFilter`: A function to remove a specific filter from the current filters.
   * - `removeAllFilters`: A function to clear all active filters.
   */
  setup() {
    const { headers } = useTableColumns("recipes", [
      { title: "ID", key: "id" },
      { title: "Name", key: "name" },
      { title: "Created at", key: "created" },
      { title: "Status", key: "disabled" },
    ]);

    const { currentFilters } = useRecipesStore();

    const {
      activeFilterChips,
      disabledValuesOptions,
      removeFilter,
      removeAllFilters,
    } = useTableFilters(currentFilters);

    return {
      headers,
      currentFilters,
      activeFilterChips,
      disabledValuesOptions,
      removeFilter,
      removeAllFilters,
    };
  },

  components: {
    DataTable,
    BaseInput,
    BaseAutocomplete,
    BaseDateInput,
    ActionViewItemInfo,
    ActionDisableRecipe,
    ActionEnableRecipe,
  },

  /**
   * Data of the component.
   *
   * @typedef {Object} Data
   * @property {Object} config - Configuration object for the project
   * @property {boolean} isSomeModalOpened - Flag indicating if a modal is opened
   * @property {RecipesStore} useRecipesStore - Recipes store
   * @property {boolean} isRecipeStatusUpdating - Is recipe status updating
   * @property {Array<Object>} formFields - Form fields configuration
   */
  data() {
    return {
      config: config,
      isSomeModalOpened: false,
      wsCleanup: null,
      useRecipesStore: useRecipesStore(),
      isRecipeStatusUpdating: false,
      formFields: [
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "id",
          label: "ID",
          required: true,
          visibility: {
            viewAction: true,
            createAction: false,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "name",
          label: "Name",
          required: true,
          visibility: {
            viewAction: true,
            createAction: false,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "created",
          label: "Created",
          required: true,
          visibility: {
            viewAction: true,
            createAction: false,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          type: "text",
          fieldName: "disabled",
          label: "Status",
          required: true,
          visibility: {
            viewAction: true,
            createAction: false,
            editAction: false,
          },
        },
      ],
    };
  },

  computed: {
    currentPage() {
      return this.useRecipesStore.currentPage;
    },
    currentPageSize() {
      return this.useRecipesStore.currentPageSize;
    },
    sortBy: {
      /**
       * Computes the sorting criteria for products.
       *
       * @returns {Array<Object>} An array with a single object containing the
       *                          sorting 'key' and 'order'.
       */
      get() {
        return [
          {
            key: this.useRecipesStore.currentSortField ?? "created",
            order: this.useRecipesStore.currentSortOrder ?? "desc",
          },
        ];
      },
      set() {},
    },

    /**
     * Returns the list of recipes.
     *
     * @returns {Array} List of recipes.
     */
    recipes() {
      return this.useRecipesStore.recipes.map((recipe) => {
        if (recipe.created) {
          recipe.created = formatDate(recipe.created);
        }

        return {
          ...recipe,
          disabled: recipe.disabled ? "Disabled" : "Enabled",
        };
      });
    },
  },

  watch: {
    /**
     * Watches for changes in the currentFilters reactive property.
     *
     * Calls the handleFetchingRecipes method whenever the currentFilters
     * property changes. The handleFetchingRecipes method fetches data for
     * the recipes table.
     */
    currentFilters: {
      handler: useDebounceFn(
        function () {
          this.handleFetchingRecipes();
        },
        500,
        { maxWait: 5000 }
      ),
      deep: true,
    },
  },

  methods: {
    /**
     * Handles the opening of any modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleModalOpening() {
      this.isSomeModalOpened = true;
    },

    /**
     * Handles the closing of any modal on the page by setting the isSomeModalOpened flag to false.
     */
    handleModalClosing() {
      this.isSomeModalOpened = false;
    },

    async updateRecipeStatus(item, isDisabled, closeModal) {
      this.isRecipeStatusUpdating = true;
      const result = await this.useRecipesStore.updateRecipeStatus(
        item,
        isDisabled
      );
      this.isRecipeStatusUpdating = false;

      if (result) {
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the recipes table with active filters & sorting after action
        await this.handleFetchingRecipes();
      }
    },

    /**
     * Fetches the list of recipes based on the current page, page size, and sorting parameters.
     * Does not perform the fetch if any modal is currently open on the page.
     *
     * @async
     */
    async handleFetchingRecipes() {
      if (this.isSomeModalOpened) {
        return;
      }
      await this.useRecipesStore.fetchRecipes();
    },

    /**
     * Updates the options for the recipes table, including page size,
     * current page, and sorting parameters. Triggers a refresh of the
     * recipes list if the data is already loaded, and resets the
     * fetch interval.
     *
     * @param {Object} options - Options object containing the updated
     * pagination and sorting parameters.
     */
    updateRecipesOptions(options) {
      this.useRecipesStore.currentPageSize = options.itemsPerPage;
      this.useRecipesStore.currentPage = options.page;
      if (options.sortBy[0]) {
        this.useRecipesStore.currentSortField = options.sortBy[0].key;
        this.useRecipesStore.currentSortOrder = options.sortBy[0].order;
      } else {
        this.useRecipesStore.currentSortField = null;
        this.useRecipesStore.currentSortOrder = null;
      }
      this.handleFetchingRecipes();
    },
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   * Sets up a WebSocket connection and listens for incoming messages.
   */

  mounted() {
    useWebSocket({
      onMessage: (data) => {
        if (data.type === "recipes") {
          if (data.action === "update"){
            this.useRecipesStore.updateRecipeData(data.data);
          } else{
            this.useRecipesStore.fetchRecipes();
          }

        }
      },
    });
  },
};
</script>

<style lang="scss" scoped></style>
