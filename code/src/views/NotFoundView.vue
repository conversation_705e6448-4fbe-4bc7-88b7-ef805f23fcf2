<template>
  <!-- Page that is shown when the user tries to access a page that does not exist. -->
  <section class="placeholder">
    <BaseHeader>
      <template #title> This page does not exist </template>
    </BaseHeader>

    <BaseButton @click="navigateToMainPage" btnType="primary">
      Back to main page
    </BaseButton>
  </section>
</template>

<script>
import BaseHeader from "@/components/common/BaseHeader.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    BaseButton,
    BaseHeader,
  },

  /**
   * @returns {{}} - empty data object
   * @description
   * This component does not use any data properties.
   * This empty data object is needed for <PERSON><PERSON> to work properly.
   */
  data() {
    return {};
  },

  methods: {
    /**
     * Navigates the user to the main page of the application.
     */
    navigateToMainPage() {
      this.$router.push("/");
    },
  },
};
</script>

<style lang="scss" scoped>
.placeholder {
  height: 200px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-grow: 1;
}
</style>
