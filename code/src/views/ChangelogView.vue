<template>
  <div class="changelogWrapper">
    <h1>Changelog</h1>
    <div class="changelogContent">
      <vue-markdown :source :options/>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import VueMarkdown from "vue-markdown-render";
import axios from "axios";

const source = ref("");

const options = {
  breaks: true,
  html: true,
  typographer: true,
  linkify: true,
};

onMounted(() => {
  axios.get("/changelog.md")
    .then((response) => {
      source.value = response.data;
    })
    .catch((error) => {
      console.error("There was a problem with the fetch operation:", error);
    });
});
</script>

<style lang="scss" scoped>
.changelogWrapper {
  display: flex;
  flex-direction: column;
}
.changelogContent {
  background-color: white;
  overflow: scroll;
  margin-top: 1rem;
  padding: 3rem;
}
</style>