<template>
  <!-- Notifications to show Errors and other type of Notifications-->
  <NotificationPanel :source-item="'product_'" />

  <!-- Products table -->
  <DataTable
    title="Products"
    emptyMsg="No products available"
    :sortBy
    :items="products"
    :itemsLength="useProductsStore.productsTotal"
    :itemsPerPage="useProductsStore.currentPageSize"
    :currentPage="useProductsStore.currentPage"
    :headers
    :formFields
    :loading="
      !useProductsStore.productsLoaded ||
      !useBrandownersStore.brandownersNamesLoaded
    "
    :hasActiveFilters="!!activeFilterChips.length"
    @selectItem="(item) => selectItem(item)"
    @removeAllFilters="removeAllFilters"
    @updateOptions="updateProductsStoreOptions"
  >
    <!-- Create action (button with modal) -->
    <template
      #createAction="{
        setSelectedItem,
        formData,
        resetFormData,
        parseFormData,
        handleUpload,
      }"
    >
      <ActionCreateItem
        modalBtnText="Create product"
        :setSelectedItem
        :formData
        :resetFormData
        :parseFormData
        :handleUpload
        @createItem="(item) => createProduct(item)"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      >
        <!-- Modal for product creation -->
        <template
          #createActionModalContent="{
            formData,
            close,
            createItem,
            handleUpload,
          }"
        >
          <!-- Form for product creation -->
          <v-form @submit.prevent="createItem">
            <!-- Form inputs (can be text input or select input) -->
            <template v-for="item in formData" :key="item">
              <BaseInput
                v-if="
                  item.visibility.createAction &&
                  !item.readonly &&
                  item.type === 'text'
                "
                v-model="item.value"
                :label="item.label"
                :type="item.type"
                :maxlength="item.maxlength"
              />

              <BaseAutocomplete
                v-if="
                  item.visibility.createAction &&
                  !item.readonly &&
                  item.type === 'select'
                "
                :multiple="false"
                :label="item.label"
                :options="item.options"
                v-model="item.value"
                :isLoading="!useBrandownersStore.brandownersNamesLoaded && !useRecipesStore.recipesNamesLoaded"
              />
            </template>

            <!-- Buttons that either create product or close modal -->
            <div class="form-buttons mt-2">
              <BaseButton @click="close">Close</BaseButton>
              <BaseButton
                type="submit"
                btnType="primary"
                :disabled="isBrandownerFieldMissing || isRecipeFieldMissing"
                :loading="isProductCreating"
              >
                Submit
              </BaseButton>
            </div>
          </v-form>
        </template>
      </ActionCreateItem>
    </template>

    <!-- Chips containing active filters -->
    <template #filtersChipsSlot>
      <v-chip
        class="vuetify-chip-wrapper"
        v-for="filterChip in activeFilterChips"
        :key="filterChip.key"
        closable
        @click:close="
          removeFilter(
            filterChip.filterType,
            filterChip.filterKey,
            filterChip.value
          )
        "
      >
        {{ filterChip.label }}
      </v-chip>
    </template>

    <!-- Filters input fields -->
    <template #filtersInputsSlot>
      <BaseInput
        v-if="config.tablesColumns.products.id"
        v-model="currentFilters.id.value"
        :label="currentFilters.id.label"
      />
      <BaseInput
        v-if="config.tablesColumns.products.name"
        v-model="currentFilters.name.value"
        :label="currentFilters.name.label"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.products.brandowner_name"
        v-model="currentFilters.brandownersNames.value"
        :label="currentFilters.brandownersNames.label"
        :options="useBrandownersStore.brandownersNames"
        :isLoading="!useBrandownersStore.brandownersNamesLoaded"
      />
      <BaseInput
        v-if="config.tablesColumns.products.product_number"
        v-model="currentFilters.customerProductId.value"
        :label="currentFilters.customerProductId.label"
      />
      <BaseInput
        v-if="config.tablesColumns.products.owner_name"
        v-model="currentFilters.ownerName.value"
        :label="currentFilters.ownerName.label"
      />
      <BaseDateInput
        v-if="config.tablesColumns.products.created"
        v-model="currentFilters.createdBefore.value"
        :label="currentFilters.createdBefore.label"
      />
      <BaseDateInput
        v-if="config.tablesColumns.products.created"
        v-model="currentFilters.createdAfter.value"
        :label="currentFilters.createdAfter.label"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.products.state"
        v-model="currentFilters.state.value"
        :label="currentFilters.state.label"
        :options="productStateOptions"
      />
    </template>

    <!-- Actions (view, copy, delete, edit) -->
    <template
      #actions="{
        item,
        setSelectedItem,
        formData,
        resetFormData,
        parseFormData,
      }"
    >
      <ActionViewItem :item="item" @selectItem="(item) => selectItem(item)" />

      <ActionCopyItem
        :isActionVisible="true"
        :item
        :setSelectedItem
        :formData="copyModalFormFields"
        :resetFormData
        :parseFormData
        @copyItem="
          (item, data, closeModal) => copyProduct(item, data, closeModal)
        "
        @onOpenModalEvent="() => handleCopyModalOpening(item)"
        @onCloseModalEvent="handleModalClosing"
      >
        <!-- Form for product copying -->
        <template #copyActionModalContent="{ formData, close, copyItem }">
          <v-sheet class="selected-item" :width="420">
            <!-- Title of modal -->
            <p class="selected-item__title">Copy item</p>

            <v-divider />

            <!-- Form for product copying -->
            <v-form @submit.prevent="copyItem(formData, item, close)">
              <!-- Form inputs (can be text input select input) -->
              <template v-for="(item, index) in formData" :key="index">
                <BaseInput
                  v-if="
                    item.visibility.editAction &&
                    !item.readonly &&
                    item.type === 'text'
                  "
                  v-model="item.value"
                  :label="item.label"
                  :type="item.type"
                  :maxlength="item.maxlength"
                />
              </template>

              <!-- Buttons for either copying product or closing modal -->
              <div class="form-buttons mt-2">
                <BaseButton @click="close">Close</BaseButton>
                <BaseButton
                  type="submit"
                  btnType="primary"
                  :loading="isProductCopying"
                >
                  Submit
                </BaseButton>
              </div>
            </v-form>
          </v-sheet>
        </template>
      </ActionCopyItem>

      <ActionDeleteItem
        :isActionVisible="true"
        entityLabel="product"
        :isDeleting="isProductDeleting"
        :item="item"
        :setSelectedItem="setSelectedItem"
        @deleteItem="(item, closeModal) => deleteProduct(item, closeModal)"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <BaseModal ref="disableModalRef">
        <template #content="{ close }">
          <v-sheet class="item-action-modal">
            <p class="item-action-modal__message">
              The product has orders - want to disabled?
            </p>

            <div class="form-buttons">
              <BaseButton @click="close">Close</BaseButton>
              <BaseButton btnType="error" @click="handleDisableProduct">
                Yes
              </BaseButton>
            </div>
          </v-sheet>
        </template>
      </BaseModal>

      <ActionEditItem
        :isActionVisible="item.state !== 'DONE'"
        :item
        :setSelectedItem
        :formData
        :resetFormData
        :parseFormData
        @updateItem="
          (item, data, closeModal) => updateProduct(item, data, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      >
        <!-- Form for product editing -->
        <template #updateActionModalContent="{ formData, close, updateItem }">
          <v-sheet class="selected-item" :width="420">
            <!-- Title of modal -->
            <p class="selected-item__title">Edit item</p>

            <v-divider />

            <!-- Form for product editing -->
            <v-form @submit.prevent="updateItem(formData, item, close)">
              <!-- Form inputs (can be text input select input) -->
              <template v-for="(item, index) in formData" :key="index">
                <BaseInput
                  v-if="
                    item.visibility.editAction &&
                    !item.readonly &&
                    item.type === 'text'
                  "
                  v-model="item.value"
                  :label="item.label"
                  :type="item.type"
                  :maxlength="item.maxlength"
                />

                <template
                  v-else-if="
                    item.visibility.editAction &&
                    !item.readonly &&
                    item.type === 'select'
                  "
                >
                  <BaseAutocomplete
                    :multiple="false"
                    :label="item.label"
                    :options="item.options"
                    v-model="item.value"
                    :isLoading="!useBrandownersStore.brandownersNamesLoaded && !useRecipesStore.recipesNamesLoaded"
                  />
                </template>
              </template>

              <!-- Buttons for either updating product or closing modal -->
              <div class="form-buttons mt-2">
                <BaseButton @click="close">Close</BaseButton>
                <BaseButton
                  type="submit"
                  btnType="primary"
                  :disabled="isBrandownerFieldMissing || isRecipeFieldMissing"
                  :loading="isProductUpdating"
                >
                  Submit
                </BaseButton>
              </div>
            </v-form>
          </v-sheet>
        </template>
      </ActionEditItem>
    </template>
  </DataTable>
</template>

<script>
import config from "@/assets/config.json";
import { useDebounceFn } from "@vueuse/core";
import BaseButton from "@/components/common/BaseButton.vue";
import BaseInput from "@/components/common/BaseInput.vue";
import BaseSelect from "@/components/common/BaseSelect.vue";
import BaseAutocomplete from "@/components/common/BaseAutocomplete.vue";
import BaseDateInput from "@/components/common/BaseDateInput.vue";
import DataTable from "@/components/DataTable.vue";
import ActionCreateItem from "@/components/actions/ActionCreateItem.vue";
import ActionViewItem from "@/components/actions/ActionViewItem.vue";
import ActionDeleteItem from "@/components/actions/ActionDeleteItem.vue";
import ActionEditItem from "@/components/actions/ActionEditItem.vue";
import NotificationPanel from "@/components/common/BaseNotifications/BaseNotification.vue";
import { useProductsStore } from "@/store/products";
import { useBrandownersStore } from "@/store/brandowners";
import useTableColumns from "@/composables/useTableColumns";
import useTableFilters from "@/composables/useTableFilters";
import { formatDate } from "@/utils/formatDate";
import { v4 as uuidv4 } from "uuid";
import useWebSocket from "@/composables/useWebSocket";
import BaseModal from "@base-components/BaseModal.vue";
import { useTemplateRef } from "vue";
import ActionCopyItem from "@/components/actions/ActionCopyItem.vue";
import {useRecipesStore} from "@/store/recipes";

export default {
  /**
   * Setup function for the ProductsView component.
   *
   * Initializes and returns reactive variables and functions related to product filters.
   *
   * @returns {Object} An object containing:
   * - `headers`: List of required headers for the table.
   * - `currentFilters`: A reactive object representing the current filters applied to the table.
   * - `activeFilterChips`: An array of active filter chips based on current filters.
   * - `productStateOptions`: List of product states available for filtering.
   * - `debouncedFetchData`: A debounced function to handle fetching data with updated filters.
   * - `removeFilter`: A function to remove a specific filter from the current filters.
   * - `removeAllFilters`: A function to clear all active filters.
   */
  setup() {
    const { headers } = useTableColumns("products", [
      { title: "Product ID", key: "id" },
      { title: "Name", key: "name" },
      { title: "Customer", key: "brandowner_name" },
      { title: "Product Number", key: "product_number" },
      { title: "Owner Name", key: "owner_name" },
      { title: "Created at", key: "created" },
      { title: "State", key: "state" },
    ]);

    const { currentFilters } = useProductsStore();

    const {
      activeFilterChips,
      productStateOptions,
      removeFilter,
      removeAllFilters,
    } = useTableFilters(currentFilters);

    return {
      headers,
      currentFilters,
      activeFilterChips,
      productStateOptions,
      removeFilter,
      removeAllFilters,
    };
  },

  components: {
    ActionCopyItem,
    BaseButton,
    BaseInput,
    BaseSelect,
    BaseAutocomplete,
    BaseDateInput,
    DataTable,
    ActionCreateItem,
    ActionViewItem,
    ActionDeleteItem,
    ActionEditItem,
    NotificationPanel,
    BaseModal,
  },

  /**
   * Data of the component.
   *
   * @typedef {Object} Data
   * @property {Object} config - Configuration object for the project
   * @property {boolean} isSomeModalOpened - Flag indicating if a modal is opened
   * @property {number | null} intervalId - Interval ID for any timers used within the component
   * @property {boolean} confirmDisableDialog - Flag indicating if a modal of disable product is opened
   * @property {ProductsStore} useProductsStore - Products store instance
   * @property {BrandownersStore} useBrandownersStore - Brandowners store instance
   * @property {RecipesStore} useRecipesStore - Recipes store instance
   * @property {string | null} activeItemId - ID of the currently active item
   * @property {Object | null} selectedProductToDisable - Select product
   * @property {boolean} imageUploaded - Flag indicating if an image has been uploaded
   * @property {Object | null} activeImageLayout - Active layout for the uploaded image
   * @property {number} layoutWidth - Width of the layout
   * @property {number} layoutHeight - Height of the layout
   * @property {Object | null} layoutForUpload - Layout object prepared for upload
   * @property {boolean} isProductCreating - Flag indicating if a product is being created
   * @property {boolean} isProductUpdating - Flag indicating if a product is being updated
   * @property {boolean} isProductDeleting - Flag indicating if a product is being deleted
   * @property {boolean} isProductCopying - Flag indicating if a product is being copied
   * @property {object | null} disableModalRef - Reference to the disable modal DOM element, used for programmatic interaction.
   * @property {Array<Object>} formFields - Configuration for form fields used in product creation/editing
   * @property {Array<Object>} copyModalFormFields - Configuration for form fields used in product copying
   */
  data() {
    return {
      config: config,
      isSomeModalOpened: false,
      intervalId: null,
      confirmDisableDialog: false,
      selectedProductToDisable: null,
      useProductsStore: useProductsStore(),
      useBrandownersStore: useBrandownersStore(),
      useRecipesStore:useRecipesStore(),
      activeItemId: null,
      imageUploaded: false,
      activeImageLayout: null,
      layoutWidth: 0,
      layoutHeight: 0,
      layoutForUpload: null,
      isProductCreating: false,
      isProductUpdating: false,
      isProductCopying: false,
      isProductDeleting: false,
      disableModalRef: useTemplateRef("disableModalRef"),
      copyModalFormFields: [
        {
          value: "",
          initialValue: "",
          options: [],
          type: "text",
          fieldName: "name",
          label: "Name",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
          maxlength: 50,
        },
        {
          value: uuidv4(),
          options: [],
          type: "text",
          fieldName: "product_number",
          label: "Product Number",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
        },
      ],
      formFields: [
        {
          value: "",
          initialValue: "New product",
          options: [],
          type: "text",
          fieldName: "name",
          label: "Name",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
          maxlength: 50,
        },
        {
          value: null,
          initialValue: null,
          options: [],
          type: "select",
          fieldName: "brandowner_id",
          label: "Customer",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
        },
        {
          value: "",
          initialValue: uuidv4(),
          options: [],
          type: "text",
          fieldName: "product_number",
          label: "Product Number",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
        },
        {
          value: null,
          initialValue: null,
          options: [],
          type: "select",
          fieldName: "recipe_id",
          label: "Papertype",
          required: true,
          visibility: {
            viewAction: false,
            createAction: true,
            editAction: true,
          },
        },
      ],
    };
  },

  computed: {
    sortBy: {
      /**
       * Computes the sorting criteria for products.
       *
       * @returns {Array<Object>} An array with a single object containing the
       *                          sorting 'key' and 'order'.
       */
      get() {
        return [
          {
            key: this.useProductsStore.currentSortField ?? "created",
            order: this.useProductsStore.currentSortOrder ?? "desc",
          },
        ];
      },
      set() {},
    },

    /**
     * Computes the URL of the active image layout.
     *
     * @returns {string} URL of the active image layout if available, otherwise an empty string.
     */
    layoutURL() {
      return this.activeImageLayout ? this.activeImageLayout.url : "";
    },

    /**
     * Takes from store a list of brandowners with their IDs as values and names as text,
     * to be used in the product form.
     *
     * @returns {Array} List of brandowners in the form of { value: number, text: string }
     */
    brandownersNames() {
      return this.useBrandownersStore.brandownersNames;
    },

    /**
     * Takes from store a list of recipes with their IDs as values and names as text,
     * to be used in the product form.
     *
     * @returns {Array} List of recipes in the form of { value: number, text: string }
     */
    recipesNames() {
      return this.useRecipesStore.recipesNames;
    },

    /**
     * Computes a list of products with their created field formatted as a human-readable date string.
     *
     * @returns {Array} List of products with created field formatted
     */
    products() {
      return this.useProductsStore.products.map((product) => {
        if (product.created) {
          product.created = formatDate(product.created);
        }
        return product;
      });
    },

    /**
     * Computes whether the submit button of the product form should be disabled.
     *
     * The button is disabled if no brandowner is selected or if the brandowner field does not exist.
     *
     * @returns {boolean} True if the submit button should be disabled, false otherwise.
     */
    isBrandownerFieldMissing() {
      const brandownerField = this.formFields.find(
        (item) => item.fieldName === "brandowner_id"
      );

      return !brandownerField || !brandownerField.value;
    },

    /**
     * Computes whether the submit button of the product form should be disabled.
     *
     * The button is disabled if no recipe is selected or if the recipe field does not exist.
     *
     * @returns {boolean} True if the submit button should be disabled, false otherwise.
     */
    isRecipeFieldMissing() {
      const recipeField = this.formFields.find(
        (item) => item.fieldName === "recipe_id"
      );

      return !recipeField || !recipeField.value;
    },
  },

  watch: {
    /**
     * Watcher for the brandowners computed property.
     *
     * Updates the options list and initial value of the brandowner_id field in the product form
     * whenever the list of brandowners changes.
     */
    brandownersNames() {
      if (this.brandownersNames.length) {
        const brandownerField = this.formFields.find(
          (item) => item.fieldName === "brandowner_id"
        );

        if (!brandownerField) return;

        brandownerField.options = this.brandownersNames;

        brandownerField.initialValue = this.brandownersNames[0].value;
      }
    },

    /**
     * Watcher for the recipes computed property.
     *
     * Updates the options list and initial value of the recipe_id field in the product form
     * whenever the list of recipes changes.
     */
    recipesNames() {
      if (this.recipesNames.length) {
        const recipeField = this.formFields.find(
          (item) => item.fieldName === "recipe_id"
        );

        if (!recipeField) return;

        recipeField.options = this.recipesNames;

        recipeField.initialValue = this.recipesNames[0].value;


      }
    },


    /**
     * Watches for changes in the currentFilters reactive property.
     *
     * Calls the fetchDataAndResetInterval method whenever the currentFilters
     * property changes. The fetchDataAndResetInterval method fetches data for
     * the products table and resets the filter interval.
     */
    currentFilters: {
      handler: useDebounceFn(
        function () {
          this.fetchData();
        },
        500,
        { maxWait: 5000 }
      ),
      deep: true,
    },
  },

  methods: {
    /**
     * Handles the opening of any modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleModalOpening() {
      this.isSomeModalOpened = true;
    },

    /**
     * Handles the opening of product copy modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleCopyModalOpening(selectedItem) {
      this.copyModalFormFields = this.copyModalFormFields.map((item) => ({
        ...item,
        value:
          item.fieldName === "name"
            ? selectedItem.name
            : item.fieldName === "recipe_id"
              ? selectedItem.recipe_id
              : item.value,
      }));

      this.isSomeModalOpened = true;
    },

    /**
     * Handler for closing Modal.
     */
    handleModalClosing() {
      this.isSomeModalOpened = false;
    },

    /**
     * Navigates to the product view page by pushing the product route with the
     * selected item's ID as a parameter.
     *
     * @param {Object} item - Selected item object containing the product ID
     */
    selectItem(item) {
      this.$router.push({ name: "product", params: { id: item.id } });
    },

    /**
     * Updates the options of the products store with the given page and items per page,
     * and fetches the products based on the updated options, after that resets the interval.
     *
     * @param {Object} options - Options object containing search query, sort field and sort order.
     */
    updateProductsStoreOptions(options) {
      this.useProductsStore.currentPageSize = options.itemsPerPage;
      this.useProductsStore.currentPage = options.page;

      if (options.sortBy[0]) {
        this.useProductsStore.currentSortField = options.sortBy[0].key;
        this.useProductsStore.currentSortOrder = options.sortBy[0].order;
      } else {
        this.useProductsStore.currentSortField = null;
        this.useProductsStore.currentSortOrder = null;
      }

      this.fetchData();
    },

    /**
     * Resets the component's data to its initial state.
     *
     * This method resets the multi-step process to the first step,
     * and clears all layout-related data and flags, preparing the component for a fresh start.
     */
    resetData() {
      this.imageUploaded = false;
      this.activeImageLayout = null;
      this.layout = null;
      this.layoutWidth = 0;
      this.layoutSize = 0;
      this.layoutHeight = 0;
      this.layoutForUpload = null;
    },
    /**
     * Creates a new product and updates the UI state accordingly.
     *
     * This function sets the `isProductCreating` flag to true, creates a new
     * product using the `useProductsStore` store, and sets the flag to false
     * after the creation is complete. If the creation is successful, it
     * updates the component's state by setting the `activeItemId` property
     * to the ID of the newly created product and increments the multi-step
     * process to the next step. The function also updates the products table
     * with active filters & sorting after the action.
     *
     * @async
     * @param {Object} item - The product data to create.
     */
    async createProduct(item) {
      item.group_id = 1;
      item.plant_id = 1;

      this.isProductCreating = true;
      const result = await this.useProductsStore.createProduct(item);

      this.isProductCreating = false;

      if (result) {
        this.activeItemId = result;

        const formFieldProductNumber = this.formFields.find(
          (formFieldItem) => formFieldItem.fieldName === "product_number"
        );

        formFieldProductNumber.initialValue = uuidv4();

        this.useProductsStore.fetchProducts();

        // redirect to ProductView
        this.$router.push(`/products/${result}`);
      }
    },

    /**
     * Updates a product with the given data and calls the closeModal function if the update is successful.
     *
     * This function assigns `group_id` and `plant_id` from the `item` to the `data` object.
     * It sets the `isProductUpdating` flag to true and calls the `updateProduct` method of
     * the `useProductsStore`. If the update is successful, it calls the provided `closeModal`
     * function and updates the products table with active filters & sorting.
     *
     * @async
     * @param {Object} item - The product data to update.
     * @param {Object} data - The new data to update the product with.
     * @param {Function} closeModal - The function to be called if the update is successful, typically to close a modal.
     */

    async updateProduct(item, data, closeModal) {
      data.group_id = item.group_id;
      data.plant_id = item.plant_id;

      this.isProductUpdating = true;
      const result = await this.useProductsStore.updateProduct(item, data);
      this.isProductUpdating = false;

      if (result) {
        // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
        await closeModal();

        // Update the products table with active filters & sorting after action
        await this.fetchData();
      }
    },

    /**
     * Copies a product using the provided data and navigates to the newly created product's page.
     *
     * Sets the `isProductCopying` flag to `true` while the copy operation is in progress,
     * and resets it to `false` once the operation completes. Calls the `copyProduct` method
     * from the `useProductsStore`, passing the original `item` and new `data`.
     *
     * If the product is successfully copied (i.e., the result is a valid product ID),
     * it navigates to the product details page using Vue Router.
     *
     * @async
     * @param {Object} item - The original product object to be copied.
     * @param {Object} data - The data to override or supplement when copying the product.
     */

    async copyProduct(item, data) {
      this.isProductCopying = true;
      const result = await this.useProductsStore.copyProduct(item, data);
      this.isProductCopying = false;

      if (result) {
        this.$router.push(`/products/${result}`);

      }
    },

    /**
     * Deletes a product with the given data and calls the closeModal function if the deletion is successful.
     *
     * This function sets the `isProductDeleting` flag to true, deletes the product using the `useProductsStore`
     * and sets the flag to false after the deletion is complete. If the deletion is successful, it calls the
     * provided `closeModal` function, then updates the products table with active filters & sorting.
     *
     * @async
     * @param {Object} item - The product data to delete.
     * @param {Function} closeModal - The function to be called if the deletion is successful (usually modal closing).
     */
    async deleteProduct(item, closeModal) {
      this.isProductDeleting = true;
      try {
        const result = await this.useProductsStore.deleteProduct(item);
        this.isProductDeleting = false;
        if (result) {
          // We need to wait before modal will be closed and the flag isSomeModalOpen set to false
          await closeModal();

          // Update the products table with active filters & sorting after action
          await this.fetchData();
        }
      } catch (error) {
        this.isProductDeleting = false;
        const status = error?.response?.status;

        if (status === 403) {
          await closeModal();
          this.$refs.disableModalRef.openModal();
          this.selectedProductToDisable = item;
        }
      }
    },

    /**
     * Handles disabling a selected product by setting its `disabled` flag to `true`.
     */

    async handleDisableProduct() {
      this.isProductDeleting = true;
      const product = this.selectedProductToDisable;
      const result = await this.useProductsStore.updateProduct(product, {
        ...product,
        disabled: true,
      });

      this.confirmDisableDialog = false;
      this.selectedProductToDisable = null;
      this.isProductDeleting = false;

      if (result) {
        this.fetchData();
      }
    },

    /**
     * Fetches data from the server and stores it in the corresponding stores.
     * This function does nothing if some modal is currently opened.
     *
     * @async
     */
    async fetchData() {
      await this.useProductsStore.fetchProducts();
      await this.useBrandownersStore.fetchAllActiveBrandownersNames();
      await this.useRecipesStore.fetchRecipesNames();
    },
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   * Sets up a WebSocket connection and listens for incoming messages.
   */

  mounted() {
    useWebSocket({
      onMessage: (data) => {
        if (data.type === "products") {
          if (data.action === "update") {
            const brandOwnerName = this.brandownersNames.find(
              ({ value }) => value === data.data.brandowner_id
            );
            this.useProductsStore.updateProductData({
              ...data.data,
              brandowner_name: brandOwnerName.text,
            });
          } else {
            this.useProductsStore.fetchProducts();
          }
        }
      },
    });
  },
};
</script>

<style lang="scss" scoped></style>
