<template>
  <!-- Orders table -->
  <DataTable
    title="Orders"
    emptyMsg="No orders available"
    :loading="!useOrdersStore.ordersLoaded"
    :sortBy
    :items="orders"
    :itemsLength="useOrdersStore.ordersTotal"
    :itemsPerPage="currentPageSize"
    :currentPage
    :headers
    :formFields
    :hasActiveFilters="!!activeFilterChips.length"
    @uploadFiles="(files, id) => uploadFiles(files, id)"
    @removeAllFilters="removeAllFilters"
    @updateOptions="updateOrdersOptions"
  >
    <!-- Chips containing active filters -->
    <template #filtersChipsSlot>
      <v-chip
        class="vuetify-chip-wrapper"
        v-for="filterChip in activeFilterChips"
        :key="filterChip.key"
        closable
        @click:close="
          removeFilter(
            filterChip.filterType,
            filterChip.filterKey,
            filterChip.value
          )
        "
      >
        {{ filterChip.label }}
      </v-chip>
    </template>

    <!-- Filters input fields -->
    <template #filtersInputsSlot>
      <BaseInput
        v-if="config.tablesColumns.orders.id"
        v-model="currentFilters.orderId.value"
        :label="currentFilters.orderId.label"
      />
      <BaseInput
        v-if="config.tablesColumns.orders.product_id"
        v-model="currentFilters.productId.value"
        :label="currentFilters.productId.label"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.orders.plant_name"
        v-model="currentFilters.plantNames.value"
        :label="currentFilters.plantNames.label"
        :options="usePlantsStore.plantsNames"
        :isLoading="!usePlantsStore.plantsNamesLoaded"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.orders.dpu_name"
        v-model="currentFilters.printerNames.value"
        :label="currentFilters.printerNames.label"
        :options="usePlantsStore.dpusNames"
        :isLoading="!usePlantsStore.dpusNamesLoaded"
      />
      <BaseInput
        v-if="config.tablesColumns.orders.order_number"
        v-model="currentFilters.customerOrderId.value"
        :label="currentFilters.customerOrderId.label"
      />
      <BaseInput
        v-if="config.tablesColumns.orders.product_number"
        v-model="currentFilters.productNumber.value"
        :label="currentFilters.productNumber.label"
      />
      <BaseInput
        v-if="config.tablesColumns.orders.owner_name"
        v-model="currentFilters.ownerName.value"
        :label="currentFilters.ownerName.label"
      />
      <BaseAutocomplete
        v-if="config.tablesColumns.orders.state"
        v-model="currentFilters.state.value"
        :label="currentFilters.state.label"
        :options="orderStateOptions"
      />
      <BaseDateInput
        v-if="config.tablesColumns.orders.created"
        v-model="currentFilters.createdBefore.value"
        :label="currentFilters.createdBefore.label"
      />
      <BaseDateInput
        v-if="config.tablesColumns.orders.created"
        v-model="currentFilters.createdAfter.value"
        :label="currentFilters.createdAfter.label"
      />
    </template>

    <!-- Actions (view info, delete, edit, print, download jobticket) -->
    <template
      #actions="{
        item,
        setSelectedItem,
        formData,
        resetFormData,
        parseFormData,
      }"
    >
      <ActionViewItemInfo
        :item="item"
        :setSelectedItem="setSelectedItem"
        :formData="[...formData, ownerName, ownerEmail]"
        @selectItem="(item) => selectOrder(item)"
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      >
        <!-- Buttons for showing order preview (with modal), located inside actions list of each table row -->
        <template #viewActionContentPreview>
          <v-sheet
            class="preview"
            v-if="csvData.length && csvHeaders.length && isCSVLoaded"
          >
            <div class="d-flex ga-2">
              <div>
                <span class="font-weight-bold">Headers:</span>
                {{ this.csvHeaders.length }}
              </div>

              <div>
                <span class="font-weight-bold">Rows:</span>
                {{ this.csvData.length }}
              </div>
            </div>

            <!-- Table consisting of CSV files headers and rows -->
            <BaseTable :tableHeaders="csvHeaders" :tableData="csvData" />
          </v-sheet>

          <!-- Label inside preview modal when no CSV data is available -->
          <v-sheet
            v-else-if="!(csvData.length && csvHeaders.length) && isCSVLoaded"
            class="d-flex justify-center align-center"
          >
            No CSV data is available
          </v-sheet>

          <!-- Label inside preview modal when CSV data is still loading -->
          <v-sheet
            v-else-if="!(csvData.length && csvHeaders.length) && !isCSVLoaded"
            class="d-flex justify-center align-center ga-3"
          >
            Checking for CSV data
            <v-progress-circular
              color="#1976D2"
              height="4"
              indeterminate
              rounded
            />
          </v-sheet>

          <!-- Button inside preview modal which allows user to download order CSV file -->
          <div
            v-if="csvData.length && csvHeaders.length && isCSVLoaded"
            class="form-buttons mt-2"
          >
            <BaseButton
              btnType="primary"
              :loading="isVardataDownloading"
              :disabled="!(csvData.length || csvHeaders.length)"
              @click="downloadOrder(item.id, item.product_id)"
            >
              Download
            </BaseButton>
          </div>
        </template>
      </ActionViewItemInfo>

      <ActionDownloadOrderJobticket
        :isDownloading="isJobticketDownloading"
        :order="item"
        :setSelectedItem="setSelectedItem"
        @downloadJobticket="
          (order, closeModal) => downloadJobticket(order, closeModal)
        "
        @onOpenModalEvent="handleModalOpening"
        @onCloseModalEvent="handleModalClosing"
      />

      <ActionGoToProduct :productId="item.product_id" />
    </template>
  </DataTable>
</template>

<script>
import config from "@/assets/config.json";
import { reactive } from "vue";
import { useDebounceFn } from "@vueuse/core";
import JSZip from "jszip";
import { useToast } from "vue-toastification";
import DataTable from "@/components/DataTable.vue";
import FileUploader from "@/components/FileUploader.vue";
import BaseTable from "@/components/common/BaseTable.vue";
import BaseButton from "@/components/common/BaseButton.vue";
import BaseInput from "@/components/common/BaseInput.vue";
import BaseAutocomplete from "@/components/common/BaseAutocomplete.vue";
import BaseDateInput from "@/components/common/BaseDateInput.vue";
import FormTimeline from "@/components/FormTimeline.vue";
import ActionViewItemInfo from "@/components/actions/ActionViewItemInfo.vue";
import ActionDownloadOrderJobticket from "@/components/actions/ActionDownloadOrderJobticket.vue";
import ActionGoToProduct from "@/components/actions/ActionGoToProduct.vue";
import { useOrdersStore } from "@/store/orders";
import { useUsersStore } from "@/store/users";
import { useFilesStore } from "@/store/files";
import { usePlantsStore } from "@/store/plants";
import useTableColumns from "@/composables/useTableColumns";
import useTableFilters from "@/composables/useTableFilters";
import { formatDate } from "@/utils/formatDate";
import { parse } from "csv/sync";
import useWebSocket from "@/composables/useWebSocket";
import { useBrandownersStore } from "@/store/brandowners";


export default {
  /**
   * Setup function for the OrdersView component.
   *
   * Initializes and returns reactive variables and functions related to order filters.
   *
   * @returns {Object} An object containing:
   * - `headers`: List of required headers for the table.
   * - `currentFilters`: A reactive object representing the current filters applied to the table.
   * - `activeFilterChips`: An array of active filter chips based on current filters.
   * - `orderStateOptions`: List of order states available for filtering.
   * - `debouncedFetchData`: A debounced function to handle fetching data with updated filters.
   * - `removeFilter`: A function to remove a specific filter from the current filters.
   * - `removeAllFilters`: A function to clear all active filters.
   */
  setup() {
    const { headers } = useTableColumns("orders", [
      { title: "Order ID", key: "id" },
      { title: "Product ID", key: "product_id" },
      { title: "Plant name", key: "plant_name" },
      { title: "Printer name", key: "dpu_name" },
      { title: "Order Number", key: "order_number" },
      { title: "Product Number", key: "product_number" },
      { title: "Owner Name", key: "owner_name" },
      { title: "Created at", key: "created" },
      { title: "State", key: "state" },
    ]);

    const { currentFilters } = useOrdersStore();

    const {
      activeFilterChips,
      orderStateOptions,
      removeFilter,
      removeAllFilters,
    } = useTableFilters(currentFilters);

    return {
      headers,
      currentFilters,
      activeFilterChips,
      orderStateOptions,
      removeFilter,
      removeAllFilters,
    };
  },

  components: {
    DataTable,
    FileUploader,
    BaseTable,
    BaseButton,
    BaseInput,
    BaseAutocomplete,
    BaseDateInput,
    FormTimeline,
    ActionViewItemInfo,
    ActionDownloadOrderJobticket,
    ActionGoToProduct,
  },

  /**
   * Data of the component.
   *
   * @typedef {Object} Data
   * @property {Object} config - Configuration object for the project
   * @property {boolean} isSomeModalOpened - Flag indicating if a modal is opened
   * @property {boolean} isJobticketDownloading - Flag indicating if the jobticket is being downloaded
   * @property {boolean} isVardataDownloading - Flag indicating if the vardata is being downloaded
   * @property {OrdersStore} useOrdersStore - Orders store
   * @property {UsersStore} useUsersStore - Users store
   * @property {FilesStore} useFilesStore - Files store
   * @property {PlantsStore} usePlantsStore - Plants store
   * @property {Toast} toast - Toast service
   * @property {Object} currentFilters - Current filters for the orders table
   * @property {Array<Object>} formFields - Form fields configuration
   * @property {Array<string>} csvHeaders - CSV headers
   * @property {Array<Object>} csvData - CSV data
   * @property {boolean} isCSVLoaded - Is CSV loaded
   */
  data() {
    return {
      config: config,
      isSomeModalOpened: false,
      isJobticketDownloading: false,
      isVardataDownloading: false,
      useOrdersStore: useOrdersStore(),
      useUsersStore: useUsersStore(),
      useFilesStore: useFilesStore(),
      usePlantsStore: usePlantsStore(),
      toast: useToast(),
      formFields: [
        {
          value: "",
          initialValue: "",
          options: [],
          type: "text",
          fieldName: "id",
          label: "Order ID",
          required: false,
          visibility: {
            viewAction: true,
            createAction: false,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          options: [],
          type: "text",
          fieldName: "order_number",
          label: "Order Number",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: true,
          },
          maxLength: 20,
        },
        {
          value: "",
          initialValue: "",
          options: [],
          type: "select",
          fieldName: "plant_id",
          label: "Plant name",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: false,
          },
        },
        {
          value: "",
          initialValue: "",
          options: [],
          type: "select",
          fieldName: "dpu_id",
          label: "Printer name",
          required: true,
          visibility: {
            viewAction: true,
            createAction: true,
            editAction: false,
          },
        },
      ],
      ownerName: {
        value: "", // will be set dynamically when the order will be selected
        type: "text",
        label: "Owner name",
        visibility: {
          viewAction: true,
          createAction: false,
          editAction: false,
        },
      },
      ownerEmail: {
        value: "", // will be set dynamically when the order will be selected
        type: "text",
        label: "Owner email",
        visibility: {
          viewAction: true,
          createAction: false,
          editAction: false,
        },
      },
      csvHeaders: [],
      csvData: [],
      isCSVLoaded: false,
    };
  },

  computed: {
    currentPage() {
      return this.useOrdersStore.currentPage;
    },
    currentPageSize() {
      return this.useOrdersStore.currentPageSize;
    },
    sortBy: {
      /**
       * Computes the sorting criteria for products.
       *
       * @returns {Array<Object>} An array with a single object containing the
       *                          sorting 'key' and 'order'.
       */
      get() {
        return [
          {
            key: this.useOrdersStore.currentSortField ?? "created",
            order: this.useOrdersStore.currentSortOrder ?? "desc",
          },
        ];
      },
      set() {},
    },
    /**
     * Computes and returns a formatted list of orders from the orders store.
     * Each order's creation date is formatted using the formatDate utility function.
     *
     * @returns {Array<Object>} A list of orders with formatted creation dates.
     */
    orders() {
      return this.useOrdersStore.orders.map((order) => {
        if (order.created) {
          order.created = formatDate(order.created);
        }

        return order;
      });
    },

    /**
     * Computes and returns a list of plant IDs and names from the plants store.
     * Each plant is represented as an object with two properties: value (the plant ID) and text (the plant name).
     * This list is used to populate the base select component inside the create order form.
     *
     * @returns {Array<Object>} A list of plant IDs and names.
     */
    plantsNames() {
      return this.usePlantsStore.plantsNames;
    },
  },

  methods: {
    /**
     * Handles the opening of any modal on the page by setting the isSomeModalOpened flag to true.
     */
    handleModalOpening() {
      this.isSomeModalOpened = true;
    },

    /**
     * Handles the closing of any modal on the page by setting the isSomeModalOpened flag to false
     * and resetting the formData.
     */
    handleModalClosing() {
      this.isSomeModalOpened = false;

      this.resetData();
    },

    /**
     * Handles the selection of an order and loads the associated CSV file and parses it for preview.
     * It also loads user data and dpus options to show user-friendly label instead of id.
     *
     * @async
     * @param {Object} item - The order item that was selected.
     */
    async selectOrder(item) {
      const userData = await this.useUsersStore.getUserInfo(item.owner_uuid);
      this.ownerName.value = userData.name;
      this.ownerEmail.value = userData.email;

      const order = await this.getOrderVardata(item);

      // We need to load dpus options so that we can show user-friendly label instead of id
      this.usePlantsStore
        .loadPlantDpusNames(item.plant_id)
        .then((plantDpusNames) => {
          this.setDpusNames(plantDpusNames);
        });

      this.isCSVLoaded = true;

      if (!order) return;

      this.activeOrderId = item.id;

      const csvData = await this.readFileAsText(order);

      const parsedCSV = this.parseCSV(csvData);

      // Set headers and data for preview
      this.csvHeaders = parsedCSV.headers;
      this.csvData = this.parseRowsForPreview(parsedCSV.rows);
    },

    /**
     * Downloads the jobticket for a given order as a zip file containing the jobticket
     * JSON file and the layout PDF file. If the jobticket is not available, a warning
     * is shown and the function exits early.
     *
     * @async
     * @param {Object} order - The order for which the jobticket should be downloaded.
     * @param {Function} closeModal - An optional function to close the modal after the
     * download is finished.
     */
    async downloadJobticket(order, closeModal) {
      this.isJobticketDownloading = true;

      const jobticket = await this.useFilesStore.getFileFromS3(
        order.product_id,
        "jobticket",
        {
          orderId: order.id,
        }
      );

      if (!jobticket) {
        this.toast.warning(
          "No jobticket for download! You need to print an order first",
          { timeout: 5000 }
        );
        return;
      }

      const print = await this.useFilesStore.getFileFromS3(
        order.product_id,
        "print"
      );

      await this.createAndDownloadZip(
        [jobticket, print],
        ["jobticket.json", "print.pdf"],
        `${order.product_id}-${order.id}.zip`
      );

      this.isJobticketDownloading = false;

      if (closeModal) {
        closeModal();
      }
    },

    /**
     * Retrieves vardata from S3 for a given order item.
     *
     * @async
     * @param {Object} item - The order item to retrieve vardata for.
     * @returns {Promise<boolean | Object>} A promise that resolves to false if vardata
     * is not present, or the vardata object if it is.
     */
    async getOrderVardata(item) {
      const params = { orderId: item.id };
      const data = await this.useFilesStore.getFileFromS3(
        item.product_id,
        "vardata",
        params
      );

      if (!data) {
        return false;
      } else {
        return data;
      }
    },

    /**
     * Takes a parsed CSV string and returns an array of arrays, where each inner
     * array is a row in the CSV file, and the elements of the inner array are the
     * columns of the CSV file in the order that they appear in the CSV file.
     *
     * @param {Object[][]} csvString - A parsed CSV string where each element is
     * an object with column names as keys and column values as values.
     * @returns {string[][]} - An array of arrays where each inner array is a row
     * in the CSV file, and the elements of the inner array are the columns of the
     * CSV file in the order that they appear in the CSV file.
     */
    parseRowsForPreview(rows) {
      return rows.map((row) => Object.values(row));
    },

    /**
     * Reads a file as text and returns a promise that resolves to the text
     * contents of the file.
     *
     * @param {File} file - The file to read.
     * @returns {Promise<string>} A promise that resolves to the text contents of
     * the file.
     */
    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (event) => {
          if (typeof event.target.result === "string") {
            resolve(event.target.result);
          } else {
            reject(new Error("Expected string content"));
          }
        };

        reader.onerror = (error) => reject(error);

        reader.readAsText(file);
      });
    },

    /**
     * Parses a CSV string and returns an object with headers and rows.
     *
     * @param {string} csvData - The CSV string to parse.
     * @returns {{ headers: string[], rows: { [key: string]: string }[] }} - An
     * object where the `headers` property is an array of the column headers in
     * the CSV file, and the `rows` property is an array of objects, where each
     * object has the column headers as keys and the column values as values.
     */
    parseCSV(csvData) {
      try {
        // Detect delimiter by checking the first line
        const firstLine = csvData.split("\n")[0];
        const delimiter = firstLine.includes(";") ? ";" : ",";

        const records = parse(csvData, {
          columns: true, // Automatically use the first row as headers
          skip_empty_lines: true, // Ignore empty lines to prevent parsing errors
          trim: true, // Remove leading and trailing whitespace from headers and fields
          delimiter: delimiter, // Set the detected delimiter
        });

        // Check if records is empty (only headers, no data)
        if (records.length === 0) {
          // Get headers from the parser's info object
          const headers =
            parse(csvData, {
              columns: false,
              skip_empty_lines: true,
              trim: true,
              delimiter: delimiter,
            })[0] || [];

          return {
            headers,
            rows: [],
          };
        }

        if (!records.length) {
          throw new Error("No data in CSV file");
        }

        const headers = Object.keys(records[0]);
        return { headers, rows: records };
      } catch (error) {
        throw new Error(`Error parsing CSV: ${error.message}`);
      }
    },

    /**
     * Resets all data for the component to initial state.
     *
     * Resets:
     * - csvData to an empty array
     * - csvHeaders to an empty array
     * - activeOrderId to null
     * - isCSVLoaded to false
     */
    resetData() {
      this.csvData = [];
      this.csvHeaders = [];
      this.activeOrderId = null;
      this.isCSVLoaded = false;
    },

    /**
     * Downloads the given blob as a file with the given filename.
     *
     * The method creates a new link element, sets its href to the blob URL, sets the download
     * attribute to the filename, sets the target to "_blank" and calls the click method to
     * download the file. After the file is downloaded, it revokes the blob URL and removes the
     * link element.
     *
     * @param {Blob} blob - The blob to download as a file.
     * @param {string} filename - The filename to use when downloading the file.
     */
    downloadFileByLink(blob, filename) {
      const link = document.createElement("a");
      link.setAttribute("href", URL.createObjectURL(blob));
      link.setAttribute("download", filename);
      link.setAttribute("target", "_blank");
      link.click();
      URL.revokeObjectURL(link.href);
      link.remove();
    },

    /**
     * Creates a ZIP archive from the given file blobs and downloads it as a file.
     *
     * The method takes an array of file blobs and an array of corresponding filenames, creates a
     * new JSZip instance, adds the blobs to the zip instance with the given filenames, generates
     * the ZIP file blob asynchronously, and downloads the file blob as a ZIP file using the
     * downloadFileByLink method.
     *
     * @async
     * @param {Blob[]} fileBlobs - The file blobs to add to the ZIP archive.
     * @param {string[]} filenames - The filenames to use for the blobs in the ZIP archive.
     * @param {string} zipFilename - The filename to use when downloading the ZIP file.
     */
    async createAndDownloadZip(fileBlobs, filenames, zipFilename) {
      const zip = new JSZip();

      fileBlobs.forEach((blob, index) => {
        zip.file(filenames[index], blob);
      });

      const zipBlob = await zip.generateAsync({ type: "blob" });

      this.downloadFileByLink(zipBlob, zipFilename);
    },

    /**
     * Updates the Dpus select options and sets the selected value.
     *
     * @param {Object[]} plantDpusNames - The Dpus values for the selected plant ID.
     */
    setDpusNames(plantDpusNames) {
      const dpuIdField = this.formFields.find(
        (formFieldItem) => formFieldItem.fieldName === "dpu_id"
      );

      dpuIdField.options = plantDpusNames;

      const selectedDpuOption = this.formFields.find(
        (item) => item.value === dpuIdField.value
      );
      if (selectedDpuOption) {
        dpuIdField.value = selectedDpuOption.value;
      }
    },

    /**
     * Handles fetching orders.
     * Fetches the list of orders from the server and updates the total amount of orders.
     * If the modal is opened, then it doesn't fetch the orders.
     *
     * @async
     */
    async handleFetchingOrders() {
      if (this.isSomeModalOpened) {
        return;
      }

      await this.useOrdersStore.fetchOrders();
      await this.usePlantsStore.fetchPlantsNames();
      await this.usePlantsStore.fetchAllDpusNames();
    },

    /**
     * Updates the options for the orders table, including page size,
     * current page, and sorting parameters. Triggers a refresh of the
     * orders list if the data is already loaded, and resets the fetch interval.
     *
     * @param {Object} options - Options object containing the updated
     * pagination and sorting parameters.
     */
    updateOrdersOptions(options) {
      this.useOrdersStore.currentPageSize = options.itemsPerPage;
      this.useOrdersStore.currentPage = options.page;

      if (options.sortBy[0]) {
        this.useOrdersStore.currentSortField = options.sortBy[0].key;
        this.useOrdersStore.currentSortOrder = options.sortBy[0].order;
      } else {
        this.useOrdersStore.currentSortField = null;
        this.useOrdersStore.currentSortOrder = null;
      }

      this.handleFetchingOrders();
    },

    /**
     * Downloads the CSV file for the given order ID.
     *
     * @async
     * @param {number} orderId - The ID of the order for which the CSV file is to be downloaded.
     * @param {number} productId - The ID of the product associated with the order.
     */
    async downloadOrder(orderId, productId) {
      this.isVardataDownloading = true;

      const params = { orderId: orderId };
      const data = await this.useFilesStore.getFileFromS3(
        productId,
        "vardata",
        params
      );

      if (data) {
        const blob = new Blob([data], { type: "text/csv" });

        this.downloadFileByLink(blob, `${orderId}.csv`);
      }

      this.isVardataDownloading = false;
    },
  },

  watch: {
    /**
     * Watches for changes in the plant names.
     *
     * If there are plant IDs available, it updates the options for the plant ID field
     * in the form fields configuration. It also triggers loading of Dpus names for the first
     * plant ID to cache the Dpus names data for efficient access.
     */
    plantsNames() {
      if (this.plantsNames.length) {
        const plantIdField = this.formFields.find(
          (item) => item.fieldName === "plant_id"
        );

        plantIdField.options = this.plantsNames;

        // Once plants are loaded, we can load Dpus names for the first plant and cache it
        this.usePlantsStore.loadPlantDpusNames(this.plantsNames[0].value);
      }
    },

    /**
     * Watches for changes in the currentFilters reactive property.
     *
     * Calls the handleFetchingOrders method whenever the currentFilters
     * property changes. The handleFetchingOrders method fetches data for
     * the orders table.
     */
    currentFilters: {
      handler: useDebounceFn(
        function () {
          this.handleFetchingOrders();
        },
        500,
        { maxWait: 5000 }
      ),
      deep: true,
    },
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   * Sets up a WebSocket connection and listens for incoming messages.
   */

  mounted() {
    useWebSocket({
      onMessage: (data) => {
        if (data.type === "orders") {
          if (data.action === "update") {
            this.useOrdersStore.updateOrderData(data.data);
          } else {
            this.useOrdersStore.fetchOrders();
          }
        }
      },
    });
  },
};
</script>

<style scoped lang="scss">
.preview {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 1rem;
}
</style>
