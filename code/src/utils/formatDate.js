import { useDateFormat } from "@vueuse/core";

const defaultFormatter = "YYYY-MM-DD"; // Base date format

/**
 * Formats a given date into a time format based solely on the browser's locale.
 *
 * @module
 * @param {Date | string | number} date - The date to format. Can be a Date object, a date string, or a timestamp.
 * @returns {string} The formatted date string based on the browser's locale.
 */
export const formatDate = (date) => {
  // Check if input matches ISO 8601 format with milliseconds and Z timezone
  const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
  if (!isoDateRegex.test(date)) {
    return date;
  }

  // Get the browser's locale (e.g., "en-US", "fr-FR", etc.)
  const browserLocale = navigator.language || "en-US";

  // Detect if the locale uses 24-hour or 12-hour time format
  const is24HourFormat = !new Intl.DateTimeFormat(browserLocale, {
    hour: "numeric",
  })
    .formatToParts(new Date())
    .some((part) => part.type === "dayPeriod"); // 'dayPeriod' exists in 12-hour format

  // Choose the time format based on the locale's convention
  const timeFormatter = is24HourFormat ? "HH:mm" : "hh:mm A";

  // Combine base date and time format
  const formatter = `${defaultFormatter} ${timeFormatter}`;

  // Ensure the input is a valid Date object
  const formattedDate = new Date(date);

  // Use the locale to format the date with useDateFormat
  return useDateFormat(formattedDate, formatter, {
    locales: browserLocale, // Use the browser's locale setting
  }).value;
};
