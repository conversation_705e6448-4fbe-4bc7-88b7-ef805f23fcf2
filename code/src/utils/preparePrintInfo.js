/**
 * @description
 * This function generates a string with HTML that represents all the necessary
 * information about a product, including its name, ID, customer name, product
 * number, state, papertype, creation date, owner info, approver info, layout
 * dimensions and preview, and orders table. The output string is then used to
 * generate a PDF file.
 *
 * @param {Object} productInfo - an object with product information
 * @param {Object} layoutInfo - an object with product layout information
 * @param {Object} ordersTableInfo - an object with information about orders table (headers and data)
 * @returns {String} - a string with HTML that is used for printing
 */
export const preparePrintInfo = (productInfo, layoutInfo, ordersTableInfo) => {
  return `
    <div class="print-container">
        <div class="pdf-section">
        <h2 class="pdf-section__title">Product Info</h2>
        <div class="info-block">
            <h3 class="info-block__headline">Product name</h3>
            <span class="info-block__text">${productInfo.name}</span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Product ID</h3>
            <span class="info-block__text">${productInfo.id}</span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Customer name</h3>
            <span class="info-block__text">
            ${productInfo.brandownerName}
            </span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Product Number</h3>
            <span class="info-block__text"
            >${productInfo.productNumber}</span
            >
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">State</h3>
            <div class="state-block">
            <span class="state-block__text">${productInfo.stateName}</span>
            <i class="state-block__icon mdi ${productInfo.stateIcon}" style="color: ${productInfo.stateIconColor}"></i>
            </div>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Papertype</h3>
            <span class="info-block__text"
            >${productInfo.recipeName}</span
            >
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Created at</h3>
            <span class="info-block__text">${productInfo.creationDate}</span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Owner</h3>
            <span class="info-block__text">
            ${productInfo.ownerInfo ? `${productInfo.ownerInfo.name} (<a href="mailto:${productInfo.ownerInfo.email}">${productInfo.ownerInfo.email}</a>)` : "(no creator info)"}
            </span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Approver</h3>
            <span class="info-block__text">
            ${productInfo.approverInfo ? `${productInfo.approverInfo.name} (<a href="mailto:${productInfo.approverInfo.email}">${productInfo.approverInfo.email}</a>)` : "(not approved yet)"}
            </span>
        </div>
        </div>
        <div class="pdf-section">
        <h2 class="pdf-section__title">Product Layout</h2>
        <div class="info-block">
            <h3 class="info-block__headline">Dimensions</h3>
            <span class="info-block__text">
            ${layoutInfo.width} in x
            ${layoutInfo.height} in
            </span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Ink Coverage</h3>
            <span class="info-block__text">
            ${productInfo.inkCoverage} %
            </span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Ink Usage</h3>
            <span class="info-block__text">
            ${productInfo.inkUsage.cyan}<br>
            ${productInfo.inkUsage.magenta}<br>
            ${productInfo.inkUsage.yellow}<br>
            ${productInfo.inkUsage.black}
            </span>
        </div>
        <div class="info-block">
            <h3 class="info-block__headline">Preview</h3>
            <img class="info-block__img" src="${layoutInfo.url}" />
        </div>
        </div>
        <div class="pdf-section">
        <h2 class="pdf-section__title">Product Orders</h2>
        <table class="orders-table">
            <thead>
            <tr>
                ${ordersTableInfo.headers.map((header) => `<th>${header.title}</th>`).join("")}
            </tr>
            </thead>
            <tbody>
            ${ordersTableInfo.data.map((order) => `<tr>${ordersTableInfo.headers.map((header) => `<td>${order[header.key]}</td>`).join("")}</tr>`).join("")}
            </tbody>
        </table>
        </div>
    </div>`;
};
