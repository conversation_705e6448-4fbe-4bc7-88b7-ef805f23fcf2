{"tablesColumns": {"products": {"id": false, "name": true, "brandowner_name": true, "product_number": true, "owner_name": true, "created": true, "state": true}, "brandowners": {"id": true, "name": true, "description": true, "address": true, "contact": true, "taxid": true, "archived": true}, "orders": {"id": false, "product_id": false, "plant_name": true, "dpu_name": true, "order_number": true, "product_number": true, "owner_name": true, "created": true, "state": true}, "recipes": {"id": true, "name": true, "created": true, "disabled": true}, "productOrders": {"id": false, "plant_name": true, "dpu_name": true, "order_number": true, "owner_name": true, "recipe_name": true, "created": true, "state": true}}}