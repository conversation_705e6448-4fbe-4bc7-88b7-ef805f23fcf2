<template>
  <!-- Sample of default select input -->
  <v-select
    :label="label"
    :items="options"
    :disabled="isDisabled"
    :loading="isLoading ? 'primary' : false"
    :readonly="isLoading"
    item-title="text"
    item-value="value"
    v-model="selectedValue"
    @change="onValueChange"
  />
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
    },
    label: {
      type: String,
      default: "Select value",
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["input", "onSelectChange"],

  /**
   * The reactive data property that stores the currently selected value of the
   * select input. It is initialized to null, and can be updated by the user
   * through the UI. The value is emitted as an `input` event to the parent
   * component, and is also passed as an argument to the `onSelectChange`
   * callback function.
   *
   * @type {Object | null}
   * @default null
   */
  data() {
    return {
      selectedValue: null,
    };
  },

  watch: {
    /**
     * Watches for changes in the selectedValue reactive data property and
     * emits an `input` event to the parent component with the new value. It
     * also calls the custom `onSelectChange` handler function with the new
     * value as an argument.
     *
     * @param {Object | null} newValue - the newly selected value
     */
    selectedValue(newValue) {
      this.$emit("input", newValue); // Emit input event for v-model binding
      this.onValueChange(newValue); // Call the custom change handler
    },
  },

  methods: {
    /**
     * Emits a custom `onSelectChange` event with the newly selected value as
     * an argument. This event is intended to be used by the parent component
     * to respond to changes in the selected value.
     *
     * @param {Object | null} value - the newly selected value
     */
    onValueChange(value) {
      this.$emit("onSelectChange", value); // Emit custom onSelectChange event
    },
  },
};
</script>

<style lang="scss" scoped></style>
