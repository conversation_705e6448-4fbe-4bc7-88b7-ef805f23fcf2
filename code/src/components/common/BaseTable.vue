<template>
  <!-- Sample of default table -->
  <div class="scrollable">
    <v-table class="table">
      <!-- Table head -->
      <thead>
        <tr>
          <th v-for="item in tableHeaders" :key="item" class="text-left">
            {{ item }}
          </th>
        </tr>
      </thead>

      <!-- Table body -->
      <tbody>
        <tr v-for="row in tableData" :key="row">
          <td v-for="item in row" :key="item">{{ item }}</td>
        </tr>
      </tbody>
    </v-table>
  </div>
</template>

<script>
export default {
  props: ["tableData", "tableHeaders"],
};
</script>

<style lang="scss" scoped>
.v-table .v-table__wrapper > table > tbody > tr > td,
.v-table .v-table__wrapper > table > thead > tr > th {
  font-size: 12px;
}

.v-table .v-table__wrapper > table > thead > tr > th {
  font-weight: bold;
}

.scrollable {
  max-height: 200px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    height: 6px;
    width: 4px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-track {
    background: #f6f6f6;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgb(118, 118, 118);
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgb(118, 118, 118);
  }

  & * {
    &::-webkit-scrollbar {
      height: 6px;
      width: 4px;
      cursor: pointer;
    }

    &::-webkit-scrollbar-track {
      background: #f6f6f6;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgb(118, 118, 118);
      cursor: pointer;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: rgb(118, 118, 118);
    }
  }
}
</style>
