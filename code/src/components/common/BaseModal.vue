<template>
  <div class="modal">
    <!-- This is a slot that can be used to open the modal (usually button) -->
    <slot name="activator" :open="openModal" />

    <!-- This is the actual modal -->
    <v-dialog v-model="dialog" width="auto">
      <v-card min-width="420" class="pa-4">
        <v-icon
          class="btn-close"
          color="#444444"
          title="Close"
          @click="closeModal"
        >
          mdi-close
        </v-icon>
        <slot name="content" :close="closeModal" />
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  /**
   * Reactive data for the component.
   *
   * @property {Boolean} dialog - Whether the modal is open or not.
   * @returns {Object}
   */
  data() {
    return {
      dialog: false,
    };
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent"],

  methods: {
    /**
     * Opens the modal by setting the `dialog` state to true.
     * Also emits `onOpenModalEvent` to let the parent component
     * know that the modal has been opened.
     */
    openModal() {
      this.dialog = true;

      this.$emit("onOpenModalEvent");
    },

    /**
     * Closes the modal by setting the `dialog` state to false.
     * Waits for 300ms before emitting the `onCloseModalEvent` event.
     * This is needed to allow the animation to finish before the parent component
     * can react to the modal being closed.
     */
    async closeModal() {
      this.dialog = false;

      await new Promise((resolve) => setTimeout(resolve, 300));
      this.$emit("onCloseModalEvent");
    },
  },
};
</script>

<style lang="scss" scoped>
.modal {
  display: flex;
}

.v-card {
  position: relative;
}

.btn-close {
  z-index: 999;
  position: absolute;
  right: 8px;
  top: 8px;
  cursor: pointer;
}
</style>
