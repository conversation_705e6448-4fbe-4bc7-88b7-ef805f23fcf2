<template>
  <!-- Sample of default input -->
  <v-text-field
    :label="label"
    :type="type"
    :rules="rules"
    :maxlength="maxlength"
  />
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: "Placeholder",
    },
    type: {
      type: String,
      default: "text",
    },
    rules: {
      type: Array,
    },
    maxlength: {
      type: Number,
      default: null,
    },
  },
};
</script>

<style lang="scss" scoped></style>
