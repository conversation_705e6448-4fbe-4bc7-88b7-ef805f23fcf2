<template>
  <!-- Sample of default autocomplete input -->
  <v-autocomplete
    :label="label"
    :items="options"
    :loading="isLoading ? 'primary' : false"
    :readonly="isLoading"
    clearable
    closable-chips
    :multiple
    item-title="text"
    item-value="value"
    v-model="selectedValues"
    @change="onValuesChange"
  />
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
    },
    label: {
      type: String,
      default: "Select value",
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
  },

  emits: ["input", "onSelectChange"],

  /**
   * Data function for the component.
   *
   * @returns {Object} An object containing the component's reactive data properties.
   * @property {Array} selectedValues - Array to hold the selected values from the autocomplete input.
   */
  data() {
    return {
      selectedValues: [],
    };
  },

  watch: {
    /**
     * Watcher for the `selectedValues` property. When it changes, it emits an
     * `input` event for v-model binding and calls the custom `onValuesChange`
     * handler with the new values as an argument.
     *
     * @param {Array} newValues - The new values of the `selectedValues` property.
     */
    selectedValues(newValues) {
      this.$emit("input", newValues); // Emit input event for v-model binding
      this.onValuesChange(newValues); // Call the custom change handler
    },
  },

  methods: {
    /**
     * Emits a custom `onSelectChange` event with the newly selected values as
     * an argument. This event is intended to be used by the parent component
     * to respond to changes in the selected values.
     *
     * @param {Object[] | null} selectedValues - the newly selected values
     */
    onValuesChange(selectedValues) {
      this.$emit("onSelectChange", selectedValues); // Emit custom onSelectChange event
    },
  },
};
</script>

<style lang="scss" scoped></style>
