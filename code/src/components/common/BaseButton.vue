<template>
  <!-- Sample of default button -->
  <v-btn
    v-if="visible"
    :class="{
      'button--regular': btnType === 'primary',
      'button--error': btnType === 'error',
    }"
    :disabled="disabled"
    :type="type"
  >
    <slot />
  </v-btn>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: "button",
    },
    btnType: {
      type: String,
      default: "regular",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.button--regular {
  padding: 4px 16px;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: 0.1s ease;
  color: #ffffff;
  background: #1976d2;
  &:hover {
    background: #1562af;
  }
}

.button--error {
  background-color: red !important;
  color: white;
}
</style>
