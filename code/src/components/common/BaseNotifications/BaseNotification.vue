<template>
  <v-alert
    v-for="(notification, index) in filteredNotifications"
    :key="`${notification.id}-${index}`"
    border="start"
    :border-color="notification.type.toLowerCase()"
    closable
    variant="tonal"
    density="compact"
    rounded="0"
    :type="notification.type.toLowerCase()"
    @click:close="notification.visible = false"
  >
    {{ getDisplayText(notification) }}
  </v-alert>
</template>

<script setup>
import { computed } from "vue";
import { useNotificationStore } from "./notifications";

const props = defineProps({
  filter: {
    type: String,
    default: "",
  },
  sourceItem: {
    type: String,
    default: "",
  },
});

const notificationStore = useNotificationStore();
const notifications = computed(() => notificationStore.notifications);

const filteredNotifications = computed(() => {
  if (!notifications.value) {
    return [];
  }

  if (props.filter === "" && props.sourceItem === "") {
    return notifications.value.filter((notification) => notification.visible);
  }

  const filteredNotes = notifications.value.filter((notification) => {
    return (
      (notification.sourceItem.startsWith(props.sourceItem) ||
        props.filter.startsWith(notification.id)) &&
      notification.visible
    );
  });
  return filteredNotes;
});

function getDisplayText(notification) {
  const result = ` ${notification.message}  (${notification.simpleDateTime || "N/A"})`;
  return result;
}
</script>

<style scoped>
.v-container {
  padding: 2px;
  max-width: 100%;
  border-radius: 0px;
}

.v-expansion-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.v-expansion-panel-text {
  border-top: 1px solid #e0e0e0;
}

.v-expansion-panel-title {
  padding: 1rem;
}

.full-width {
  width: 100%;
  margin: 0;
}

:deep(.v-expansion-panel-text__wrapper) {
  padding: 0 !important;
}
</style>
