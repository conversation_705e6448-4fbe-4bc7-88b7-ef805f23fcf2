import { define<PERSON><PERSON> } from "pinia";
import { Notification } from "./models/Notification";

export const useNotificationStore = defineStore("notification", {
  state: () => ({
    notifications: [],
  }),
  actions: {
    /**
     * @param { Notification } notification
     */
    addNotification(notification) {
      this.notifications.push(notification);
    },
    /**
     *
     * @param {string} message
     * @param {string} type
     * @param {string} sourceItem
     * @param {string} id
     * @param {Date} dateTime
     * @param {boolean} visible
     */
    addNotificationByValues(message, type, sourceItem = "", id = "", dateTime = new Date(), visible = true) {
      const notification = new Notification(message, type, sourceItem, id, dateTime, visible);
      this.notifications.push(notification);
    },
    /**
     * @param {number} index
     */
    removeNotification(index) {
      this.notifications[index].visible = false;
    },
    /**
     * @param {any} id
     */
    removeNotificationById(id) {
      const notification = this.notifications.find((notification) => notification.id === id);

      if (notification) {
        notification.visible = false;
      }
    },
    getNotificationById(id) {
      return this.notifications.find((notification) => notification.id.startsWith(id));
    },

    deleteNotificationById(id) {
      this.notifications = this.notifications.filter((notification) => !notification.id.startsWith(id));
    }
  },

  persist: {
    storage: localStorage,
    key: "notificationStore",
    serializer: {
      serialize: (state) => {
        return JSON.stringify({
          notifications: state.notifications.map((notification) => ({
            message: notification.message,
            type: notification.type,
            sourceItem: notification.sourceItem,
            id: notification.id,
            dateTime: notification.dateTime,
            visible: notification.visible,
          })),
        });
      },
      deserialize: (data) => {
        const parsed = JSON.parse(data);
        return {
          notifications: parsed.notifications.map(
            (obj) =>
              new Notification(
                obj.message,
                obj.type,
                obj.sourceItem,
                obj.id,
                new Date(obj.dateTime),
                obj.visible
              )
          ),
        };
      },
    },
  },
});
