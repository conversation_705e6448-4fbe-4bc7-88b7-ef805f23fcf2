import {ALERT_STATE} from "./enums";
import {v4 as uuid} from "uuid";

export class Notification {
  /**
   * @param {string} message - The notification message.
   * @param {ALERT_STATE} type - The type of alert.
   * @param {string} [sourceItem=''] - The source component of the notification.
   * @param {string} [id=''] - The ID of the notification.
   * @param {Date} [date=new Date()] - The date and time of the notification.
   * @param {boolean} [visible=true] - The visibility of the notification.
   */
  constructor(message, type, sourceItem = "", id = "", date = new Date(), visible = true) {
    this.message = message;
    this.type = type;
    this.sourceItem = sourceItem;
    // to prevent false behaviour in vue 3 cause of rerendering
    if (id === '') {
      this.id = uuid();
    } else {
      this.id = id;
    }
    this.dateTime = date;
    this.visible = visible;
  }

  get simpleDateTime() {
    return this.dateTime.toLocaleString([],
      {year: 'numeric', month: 'numeric', day: 'numeric', hour: '2-digit', minute: '2-digit'});
  }

  get formattedDate() {
    return this.dateTime.toDateString();
  }

  get formattedTime() {
    return this.dateTime.toTimeString();
  }
}
