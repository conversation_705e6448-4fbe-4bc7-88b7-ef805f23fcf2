<template>
  <!-- Sample of table (server-based) that consists of some data and modals -->
  <v-data-table-server
    :headers="headers"
    :items="items"
    :items-length="itemsLength"
    :loading="loading"
    :sort-by="sortTableBy"
    sort-asc-icon="mdi-arrow-up-thin"
    sort-desc-icon="mdi-arrow-down-thin"
    :items-per-page="itemsPerPage"
    :items-per-page-options="perPageOptions"
    :page="currentPage"
    class="data-table"
    @update:options="updateOptions"
  >
    <!-- Table header: title, create button (with modal) and filters panel -->
    <template v-slot:top>
      <v-toolbar flat class="pr-2">
        <!-- Table title -->
        <v-toolbar-title v-if="title">{{ title }}</v-toolbar-title>

        <v-spacer />

        <!-- Create button (with modal) -->
        <slot
          name="createAction"
          :setSelectedItem="setSelectedItem"
          :formData="formData"
          :resetFormData="resetFormData"
          :parseFormData="parseFormData"
          :handleUpload="handleUpload"
        />
      </v-toolbar>

      <!-- Panel with filters -->
      <v-expansion-panels :key="filterPanelKey" v-model="filterPanelState">
        <v-expansion-panel>
          <template #title>
            <div class="filters-labels-wrapper">
              <BaseButton
                class="filters-labels-wrapper__clear-all-btn"
                :disabled="!hasActiveFilters"
                btnType="primary"
                @click.stop="() => $emit('removeAllFilters')"
              >
                Clear all
              </BaseButton>
              <span class="filters-labels-wrapper__label">Active filters:</span>
              <slot name="filtersChipsSlot">
                <span>(none)</span>
              </slot>
            </div>
          </template>
          <template #text>
            <div class="filter-inputs-wrapper">
              <slot name="filtersInputsSlot" />
            </div>
          </template>
        </v-expansion-panel>
      </v-expansion-panels>
    </template>

    <!-- Part with table headers and rows -->
    <template v-slot:item="{ item }">
      <!-- Table row settings -->
      <tr class="data-table__row" @click="(e) => navigateToItem(e, item)">
        <!-- Data cells inside current row -->
        <template v-for="(field, index) in headers" :key="field.key">
          <td v-if="index < headers.length - (hasStatusHeader ? 2 : 1)">
            {{ item[field.key] }}
          </td>
        </template>

        <!-- Data cell with status icon and tooltip (if it exists) -->
        <td v-if="hasStatusHeader">
          <span>
            <!-- Status icon -->
            <v-icon v-if="item.stateObj" :color="item.stateObj.iconColor">
              {{ item.stateObj.icon }}
            </v-icon>

            <!-- Status tooltip -->
            <v-tooltip v-if="item.stateObj" activator="parent" location="end">
              {{ item.stateObj.message }}
            </v-tooltip>
          </span>
        </td>

        <!-- Date cell with horizontal list of possible actions (each page has its own actions) -->
        <td>
          <div class="icons">
            <slot
              name="actions"
              :item="item"
              :setSelectedItem="setSelectedItem"
              :formData="formData"
              :resetFormData="resetFormData"
              :parseFormData="parseFormData"
            />
          </div>
        </td>
      </tr>
    </template>

    <!-- Table loading indicator (shows skeleton while data for table is loading) -->
    <template v-slot:loading>
      <v-skeleton-loader type="table-row@10" />
    </template>

    <!-- Empty message (shown when data for table is empty) -->
    <template v-slot:no-data>
      <div class="data-table--empty">{{ emptyMsg }}</div>
    </template>
  </v-data-table-server>
</template>

<script>
import BaseButton from "@/components/common/BaseButton.vue";
import { formatDate } from "@/utils/formatDate";

export default {
  components: {
    BaseButton,
  },

  props: {
    title: String,
    items: Array,
    itemsLength: {
      type: Number,
      default: 10,
    },
    itemsPerPage: {
      type: Number,
      default: 10,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    headers: Array,
    formFields: Array,
    emptyMsg: String,
    sortBy: Array,
    hasActiveFilters: {
      type: Boolean,
      default: false,
    },
    selectedProductId: {
      type: String,
    },
  },

  emits: ["removeAllFilters", "uploadFiles", "selectItem", "updateOptions"],

  /**
   * Data object for the DataTable component.
   * @property {Boolean} loading - Indicates whether table data is being loaded.
   * @property {Function} formatDate - Function to format date strings.
   * @property {Object} selectedItem - The item currently selected in the table.
   * @property {Array} sortingValue - The current values to sort the table by.
   * @property {Boolean} hasStatusHeader - Whether the table has a "state" header.
   * @property {Array} perPageOptions - Options for the number of items per page.
   * @property {number} filterPanelKey - A unique key for the `v-expansion-panels` component.
   * @property {Array<number>} filterPanelState - The `v-model` state for `v-expansion-panels`.
   * @property {String} selectedProductId - id of the selected product.
   * */
  data() {
    return {
      loading: true,
      formatDate: formatDate,
      selectedItem: null,
      sortingValue: this.sortBy,
      hasStatusHeader: false,
      perPageOptions: [
        { value: 10, title: "10" },
        { value: 25, title: "25" },
        { value: 50, title: "50" },
        { value: 100, title: "100" },
      ],
      filterPanelKey: 0,
      filterPanelState: [],
    };
  },

  /**
   * Lifecycle hook that is called when the component is mounted.
   *
   * This hook is called after the component has been mounted. It checks if the
   * table has a "state" header and sets the hasStatusHeader property based on
   * the result.
   */
  mounted() {
    this.hasStatusHeader = JSON.stringify(this.headers).includes("state");
  },

  computed: {
    sortTableBy: {
      /**
       * Returns the current sorting values.
       * @type {Array}
       */
      get() {
        return this.sortingValue;
      },
      /**
       * Sets the sorting value to the specified value.
       * This allows for updating the current sorting criteria
       * used in the table.
       * @param {Array} value - The new sorting values to be set.
       */
      set(value) {
        this.sortingValue = value;
      },
    },

    formData: {
      /**
       * Returns an array of form fields with their values set to the current
       * values of the selected item. If the selected item does not have a value
       * for a field, the field's value will be set to its initial value.
       * @type {Array}
       */
      get() {
        if (this.selectedItem && this.formFields) {
          return this.formFields.map((field) => {
            if (this.selectedItem[field.fieldName]) {
              field.value = this.selectedItem[field.fieldName];
            } else {
              field.value = field.initialValue;
            }
            return field;
          });
        } else {
          return [];
        }
      },
      set() {},
    },
  },

  watch: {
    /**
     * Watches for changes in the 'items' array.
     * Sets the loading state to false when the items array is populated.
     * @param {Array} value - The new array of items.
     */
    items(value) {
      if (value.length) {
        this.loading = false;
      }
    },
    /**
     * Watches for changes in `filterPanelKey`.
     * When the key changes, it explicitly sets `filterPanelState` to an empty array `[]`
     * to ensure the filter panel is collapsed after its re-creation.
     */
    filterPanelKey() {
      this.filterPanelState = [];
    },

    /**
     * Watches for changes in `selectedProductId`.
     * When the selectedProductId changes, a function will be called to reset the filters.
     */
    selectedProductId(newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        this.resetFilterPanel();
      }
    },
  },

  methods: {
    /**
     * Method to forcefully reset the state of the filter panel.
     */
    resetFilterPanel() {
      this.filterPanelKey++;
      this.filterPanelState = [];
    },
    /**
     * Setter for the selected item. Used inside the actions slot to change the selected item directly.
     * @param {object} newSelectItem - The new selected item.
     */
    setSelectedItem(newSelectItem) {
      this.selectedItem = newSelectItem;
    },

    /**
     * Parses an array of form data objects into a single object with field names as keys.
     *
     * @param {Array} data - An array of objects, each containing 'fieldName' and 'value' properties.
     * @returns {Object} An object where each key is a 'fieldName' and its corresponding value is the 'value' from the input array.
     */
    parseFormData(data) {
      const parsedData = data.reduce((acc, item) => {
        acc[item.fieldName] = item.value;
        return acc;
      }, {});

      return parsedData;
    },

    /**
     * Handles the file upload process. Finds the corresponding field in the formData
     * array and assigns the uploaded files to its value. Then emits the 'uploadFiles'
     * event with the uploaded files and the id of the item being edited.
     * @param {Array} files - An array of uploaded files.
     * @param {Object} item - The form field corresponding to the uploaded files.
     * @param {string} id - The id of the item being edited.
     */
    handleUpload(files, item, id) {
      const index = this.formData.findIndex(
        (formItem) => formItem.fieldName === item.fieldName
      );

      this.formData[index].value = files;
      this.$emit("uploadFiles", files, id);
    },

    /**
     * Resets all the form fields to their initial values.
     */
    resetFormData() {
      this.formData.forEach((item) => {
        //quickfix dpw-1013
        if (item.fieldName === "order_number") {
          return;
        }
        item.value = item.initialValue;
      });
    },

    /**
     * Navigates to the selected item by checking if the event target is an icon.
     * If not, it emits the 'selectItem' event with the given item.
     * @param {Event} e - The event triggered by clicking on an item.
     * @param {Object} item - The item to select.
     */
    navigateToItem(e, item) {
      const target = e.target.closest(".icons__item");

      if (!target) {
        this.$emit("selectItem", item);
      }
    },

    /**
     * Emits the 'updateOptions' event with the given options (filters, sort, etc.).
     * @param {Object} options - The options to update.
     */
    updateOptions(options) {
      this.$emit("updateOptions", options);
    },
  },
};
</script>

<style lang="scss" scoped>
.filters-labels-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  &__clear-all-btn {
    margin-right: 10px;
  }
  &__label {
    margin-right: 10px;
    font-size: 16px;
  }
}

.filter-inputs-wrapper {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  column-gap: 20px;
}

.icons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.item {
  font-weight: 600;

  &--active {
    color: #1d9f62;
  }

  &--invited {
    color: #ffc107;
  }

  &--removed {
    color: #dc3545;
  }
}

.data-table {
  :deep(.v-toolbar__content) {
    padding: 0;
  }

  :deep(.v-toolbar-title) {
    margin: 0;
  }

  :deep(.v-table__wrapper::-webkit-scrollbar) {
    height: 6px;
    width: 6px;
    cursor: pointer;
  }

  :deep(.v-table__wrapper::-webkit-scrollbar-track) {
    background: #f5f5f5;
  }

  :deep(.v-table__wrapper::-webkit-scrollbar-thumb) {
    background-color: #3e4956;
    cursor: pointer;

    &:hover {
      background-color: #272e36;
    }
  }

  :deep(thead tr) {
    background-color: #f5f5f5;
  }

  :deep(thead tr th span) {
    text-wrap: nowrap;
  }

  tbody tr {
    cursor: pointer;
  }

  tbody tr:nth-child(odd) {
    background-color: #ffffff;
  }

  tbody tr:nth-child(even) {
    background-color: #f5f5f5;
  }

  tbody tr:hover {
    background-color: #f5f5f5;
  }

  tbody tr td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 350px;
  }

  &--empty {
    padding: 2rem 0;
  }
}
</style>
