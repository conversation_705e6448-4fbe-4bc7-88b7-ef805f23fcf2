<script setup lang="ts">
import { computed, toRaw, onBeforeUnmount } from "vue";
import { useProductsStore } from "@/store/products";
import { onMounted } from "vue";

const props = defineProps<{
  label: string;
  modelValue: any[];
}>();

const emit = defineEmits<{
  (e: "input", value: any[]): void;
  (e: "update:modelValue", value: any[]): void;
}>();

const useProducts = useProductsStore();
const autoCodeData = computed(() => {
  return useProducts.autoCodes.map(
    (autoCode: {
      id: string;
      variable_name: string;
      vdp_type: string;
      config: {
        prefix: string;
        suffix: string;
        codeMaxLength: string;
        filledWithZeros: boolean;
        incrementLength: number;
        incrementStart: number;
        incrementStep: number;
        lastIncrementPrinted: number;
      };
    }) => {
      const incrementText = "0".repeat(autoCode.config.incrementLength);
      const filteredVariableName = autoCode.variable_name.replace("AUTO_CODE_INCREMENT_", "");
      return {
        id: autoCode.id,
        variableName: filteredVariableName,
        incrementStart: autoCode.config.lastIncrementPrinted,
        incrementStep: autoCode.config.incrementStep,
      };
    }
  );
});
const updateValue = () => {
  autoCodeData; // Access rows to ensure reactivity
  emit("input", toRaw(autoCodeData.value));
  emit("update:modelValue", toRaw(autoCodeData.value)); // Emit the updated value
};

onBeforeUnmount(() => {
  autoCodeData;
  emit("input", toRaw(autoCodeData.value));
  emit("update:modelValue", toRaw(autoCodeData.value));
});
</script>

<template>
  <div>
    <p class="label">{{ props.label }}</p>
    <table class="config-table">
      <thead>
        <tr>
          <th>Variable Name</th>
          <v-tooltip bottom>
            <template #activator="{ props }">
              <th v-bind="props" class="start-table-header" data-tip="This is the text of the tooltip">Start</th>
            </template>
            <span>If set to -1, the starting point is automatically selected</span>
          </v-tooltip>
          <th class="step-table-header">Step</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, rowIndex) in autoCodeData" :key="rowIndex">
          <td>
            <span> {{ row.variableName }} </span>
          </td>
          <td>
            <input
              data-tip="This is the text of the tooltip"
              type="number"
              v-model.number="row.incrementStart"
              class="input"
              @input="updateValue()"
            />
          </td>
          <td>
            <input
              type="number"
              v-model.number="row.incrementStep"
              class="input"
              @input="updateValue()"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<style scoped>
.label {
  font-size: 20px;
  font-weight: 400;
  text-align: left;
  margin-top: 0rem;
}
.config-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}
.config-table th,
.config-table td {
  text-align: left;
}
.config-table th {
  background-color: #f4f4f4;
}
.input {
  width: 90%;
  padding: 5px;
  font-size: 14px;
  appearance: textfield; /* Ensures spinner controls are visible */
  -moz-appearance: textfield; /* For Firefox */
  -webkit-appearance: textfield; /* For WebKit browsers */
  border-bottom: 1px solid #ccc;
}

.input::-webkit-inner-spin-button,
.input::-webkit-outer-spin-button {
  -webkit-appearance: auto; /* Ensures spinner controls are always visible */
  margin: 0;
}

.step-table-header,
.start-table-header {
  width: 6rem !important;
}
</style>
