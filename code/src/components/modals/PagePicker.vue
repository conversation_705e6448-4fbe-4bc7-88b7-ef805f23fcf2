<template>
  <v-dialog
    v-model="dialog"
    persistent
    width="auto"
    transition="false"
    no-click-animation
  >
    <v-card class="selected-item pa-4">
      <v-icon
        class="btn-close"
        color="#444444"
        title="Close"
        @click="close(null)"
      >
        mdi-close
      </v-icon>
      <v-card-title class="selected-item__title pa-0">Select page</v-card-title>
      <v-divider></v-divider>
      <p class="progressarea">
        Your uploaded PDF consists of multiple pages. Please select the page you want to use as the layout
      </p>
      <!-- Flex container for images -->
      <div class="image-container">
        <img
          ref="imageRefs"
          tabindex="0"
          class="image"
          :class="{ selected: isSelected(index + 1) }"
          v-for="(imageURL, index) in imageURLS"
          :key="index + 1"
          @keyup.enter="chooseFile"
          @keyup.esc="close(null)"
          @keydown="handleKeydown(index, $event)"
          @click="selectImage(index + 1)"
          :src="imageURL"
        />

      </div>
      <v-card-actions>
        <v-spacer />
        <div class="form-buttons mt-2">
          <BaseButton @click="close(null)" variant="elevated">
            Cancel
          </BaseButton>

          <BaseButton @click="chooseFile" btnType="primary" variant="elevated">
            Confirm
          </BaseButton>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import useFileConversion from "@/composables/useFileConversion";
import BaseButton from "@/components/common/BaseButton.vue";

const props = defineProps({
  file: Object,
  pageCount: Number,
});

const emit = defineEmits(["close"]);

const { toBase64, pdfToImage } = useFileConversion();

const dialog = ref(false);
const selected = ref(1);
const imageURLS = ref([]);
const pdfURL = ref("");
const imageRefs = ref([]);

onMounted(async () => {
  dialog.value = true;

  pdfURL.value = await toBase64(props.file);
  for (let i = 1; i <= props.pageCount; i++) {
    imageURLS.value.push(await pdfToImage(pdfURL.value, i));
  }

  nextTick(() => {
    if (imageRefs.value[0]) {
      imageRefs.value[0].focus();
      selected.value = 1; // Auto-select the first image
    }
  });
});

const isSelected = (index) => {
  return index === selected.value;
};

// Function to set image references dynamically
function setImageRef(el) {
  if (el) {
    imageRefs.value.push(el);
  }
}

function selectImage(index) {
  selected.value = index;
}

async function chooseFile() {
  if (!selected.value) return;

  dialog.value = false;
  setTimeout(() => emit("close", selected.value), 200);
}

function close(result) {
  dialog.value = false;
  setTimeout(() => emit("close", result), 200);
}

function handleKeydown(index, event) {
  if (event.key === "Tab" || event.key === "ArrowRight") {
    event.preventDefault();
    const nextIndex = (index + 1) % imageRefs.value.length;
    imageRefs.value[nextIndex].focus();
    selected.value = nextIndex + 1;
  } else if (event.key === "ArrowLeft") {
    const prevIndex = (index - 1 + imageRefs.value.length) % imageRefs.value.length;
    imageRefs.value[prevIndex].focus();
    selected.value = prevIndex + 1;
  }
}

// Wait for the DOM to update and focus the first image
nextTick(() => {
  if (imageRefs.value[0]) {
    imageRefs.value[0].focus();
    selected.value = 1; // Auto-select the first image
  }
});
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  font-weight: 400;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

.image {
  width: 150px;
  height: 150px;
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 0.5rem;
  margin: 0.5rem;
  object-fit: contain;
}

.image:hover {
  transform: scale(1.1);
}

.selected {
  border: 2px solid #1976d2;
}

.btn-close {
  z-index: 999;
  position: absolute;
  right: 8px;
  top: 8px;
  cursor: pointer;
}

.progressarea {
  text-align: center; /* Ensures text is centered */
    font-size: 14px;
    color: #1976d2;
    margin-bottom: 4px;
}
</style>
