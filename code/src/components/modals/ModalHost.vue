<!-- components/ModalHost.vue -->
<template>
  <component class="wrapper"
    v-if="modalComponent"
    :is="modalComponent"
    v-bind="modalProps"
    @close="handleClose"
  />
</template>

<script setup>
import {ref, shallowRef} from 'vue'

const modalComponent = shallowRef(null)
const modalProps = ref({})
let currentResolve = null

function open(component, props = {}) {
  return new Promise((resolve) => {
    modalComponent.value = component
    modalProps.value = props
    currentResolve = resolve
  })
}

function handleClose(result) {
  modalComponent.value = null
  modalProps.value = {}
  if (currentResolve) {
    currentResolve(result)
    currentResolve = null
  }
}

defineExpose({ open })
</script>
