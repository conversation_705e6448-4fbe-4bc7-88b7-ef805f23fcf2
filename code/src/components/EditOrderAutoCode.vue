<script setup lang="ts">
import { toRaw,  onBeforeUnmount, ref, computed, reactive, onMounted } from "vue";
import { useProductsStore } from "@/store/products";

const props = defineProps<{
  label: string;
  modelValue: any[];
  formData: any;
  productId: string;
}>();

const emit = defineEmits<{
  (e: "input", value: any[]): void;
  (e: "update:modelValue", value: any[]): void;
}>();


const useProducts = useProductsStore();
const selectedOrderAutoCodes = ref([]); // Reactive fallback for selectedOrderAutoCodes

// Extract the order ID from formData
const orderIdObject = props.formData.find((item) => item.fieldName === "id");
const orderId = orderIdObject ? orderIdObject.value : null;

// Fetch auto codes when the component is mounted
onMounted(async () => {
  if (orderId) {
    await useProducts.getSelectedOrderAutoCodes(props.productId, orderId);
    selectedOrderAutoCodes.value = useProducts.selectedOrderAutoCodes || [];
    autoCodeData; // Access rows to ensure reactivity
    emit("input", toRaw(autoCodeData.value));
    emit("update:modelValue", toRaw(autoCodeData.value)); // Emit the updated value
  }
});

// Computed property for autoCodeData
const autoCodeData = computed(() => {
  return selectedOrderAutoCodes.value.map((item: { id: number; variableName: string; incrementStart: number; incrementStep: number }) => {
    return {
      id: item.id,
      variableName: item.variableName,
      incrementStart: item.incrementStart,
      incrementStep: item.incrementStep,
    };
  });
});

const updateValue = (rowValue: number, event: Event) => {
  autoCodeData; // Access rows to ensure reactivity
  emit("input", toRaw(autoCodeData.value));
  emit("update:modelValue", toRaw(autoCodeData.value)); // Emit the updated value
};

onBeforeUnmount(() => {
  autoCodeData;
  emit("input", toRaw(autoCodeData.value));
  emit("update:modelValue", toRaw(autoCodeData.value));
});
</script>

<template>
  <div>
    <p class="label">{{ props.label }}</p>
    <table class="config-table">
      <thead>
        <tr>
          <th>Variable Name</th>
          <v-tooltip bottom>
            <template #activator="{ props }">
              <th v-bind="props" class="start-table-header" data-tip="This is the text of the tooltip">Start</th>
            </template>
            <span>If set to -1, the starting point is automatically selected</span>
          </v-tooltip>
          <th class="step-table-header">Step</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, rowIndex) in autoCodeData" :key="rowIndex">
          <td>
            <span> {{ row.variableName }} </span>
          </td>
          <td>
            <input
              type="number"
              v-model.number="row.incrementStart"
              class="input"
              @input="updateValue(row.incrementStart, $event)"
            />
          </td>
          <td>
            <input
              type="number"
              v-model.number="row.incrementStep"
              class="input"
              @input="updateValue(row.incrementStep, $event)"
            />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<style scoped>
.label {
  font-size: 20px;
  font-weight: 400;
  text-align: left;
  margin-top: 0rem;
}
.config-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}
.config-table th,
.config-table td {
  text-align: left;
}
.config-table th {
  background-color: #f4f4f4;
}
.input {
  width: 90%;
  padding: 5px;
  font-size: 14px;
  appearance: textfield; /* Ensures spinner controls are visible */
  -moz-appearance: textfield; /* For Firefox */
  -webkit-appearance: textfield; /* For WebKit browsers */
  border-bottom: 1px solid #ccc;
}

.input::-webkit-inner-spin-button,
.input::-webkit-outer-spin-button {
  -webkit-appearance: auto; /* Ensures spinner controls are always visible */
  margin: 0;
}

.step-table-header,
.start-table-header {
  width: 6rem !important;
}
</style>
