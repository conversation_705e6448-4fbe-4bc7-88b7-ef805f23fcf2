<template>
  <!-- DataTable brandowner activation action (opens modal with confirmation for brandowner activation) -->
  <BaseModal
    v-if="brandowner.archived === 'Deactivated'"
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- But<PERSON> that opens modal with confirmation for brandowner activation -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Activate"
        @click.stop="openActivateModal(brandowner, open)"
      >
        <IconEntityDisabled class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with confirmation for brandowner activation -->
    <template #content="{ close }">
      <v-sheet class="item-action-modal">
        <p class="item-action-modal__message">
          Are you sure you want to activate customer?
        </p>

        <div class="form-buttons">
          <BaseButton @click="close">Close</BaseButton>
          <BaseButton
            btnType="primary"
            :loading="isUpdating"
            @click="activateBrandowner(formData, brandowner, close)"
          >
            Activate
          </BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconEntityDisabled from "@/components/icons/IconEntityDisabled.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconEntityDisabled,
    BaseModal,
    BaseButton,
  },

  props: {
    isUpdating: {
      type: Boolean,
      required: true,
    },
    brandowner: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
    parseFormData: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "updateBrandowner"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the update modal window by setting the selected item to the given
     * brandowner and calling the given callback function.
     * @param {Object} brandowner - The brandowner to update.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openActivateModal(brandowner, callback) {
      this.setSelectedItem(brandowner);
      callback();
    },

    /**
     * Activates the brandowner by emitting an 'updateBrandowner' event with parsed
     * data and the brandowner to be activated and a callback to close the modal
     * window.
     *
     * @param {Object} data - The data to update the brandowner with.
     * @param {Object} brandowner - The brandowner to be activated.
     * @param {function} close - The function to call when the modal window is
     * closed.
     */
    activateBrandowner(data, brandowner, close) {
      const parsedData = this.parseFormData(data);

      parsedData.archived = false;

      this.$emit("updateBrandowner", brandowner, parsedData, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
