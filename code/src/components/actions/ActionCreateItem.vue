<template>
  <!-- DataTable create action (with modal) -->
  <BaseModal
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- Button of modal for element creation -->
    <template #activator="{ open }">
      <BaseButton
        :disabled="isModalBtnDisabled"
        btnType="primary"
        @click="openCreateModal(open)"
      >
        {{ modalBtnText }}
      </BaseButton>
    </template>

    <!-- Form with element creation options -->
    <template #content="{ close }">
      <v-sheet class="selected-item" :width="420">
        <p class="selected-item__title">{{ modalBtnText }}</p>

        <!-- Content of create form (can differ in different tables) -->
        <slot
          name="createActionModalContent"
          :formData="formData"
          :close="() => closeModal(close)"
          :createItem="() => createItem(formData, close)"
          :handleUpload="handleUpload"
        />
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    BaseModal,
    BaseButton,
  },

  props: {
    isModalBtnDisabled: {
      type: Boolean,
      default: false,
    },
    modalBtnText: {
      type: String,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
    resetFormData: {
      type: Function,
      required: true,
    },
    parseFormData: {
      type: Function,
      required: true,
    },
    handleUpload: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "createItem"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the create modal window by setting the selected item to an empty object
     * and calling the given callback function.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openCreateModal(callback) {
      this.setSelectedItem({});
      callback();
    },

    /**
     * Resets the form data to its initial state and calls the given callback
     * function. Used to close the modal window.
     * @param {function} callback - The function to call when the modal window is
     * closed.
     */
    closeModal(callback) {
      callback();
      this.resetFormData();
    },

    /**
     * Creates a new item in the table by emitting the 'createItem' event with the parsed form data and the close function.
     * @param {Array} data - An array of objects, each containing 'fieldName' and 'value' properties.
     * @param {function} close - The function to call when the modal window is closed.
     */
    createItem(data, close) {
      const parsedData = this.parseFormData(data);

      this.$emit("createItem", parsedData, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
