<template>
  <!-- DataTable copy action (opens modal with options for item copying) -->
  <BaseModal
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
    v-if="isActionVisible"
  >
    <!-- Button that opens modal with options for item copying -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Copy"
        @click.stop="openUpdateModal(item, open)"
      >
        <IconCopy class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with options for copy item -->
    <template #content="{ close }">
      <slot
        name="copyActionModalContent"
        :formData="formData"
        :close="() => closeModal(close)"
        :copyItem="() => copyItem(formData, item, close)"
      />
    </template>
  </BaseModal>
</template>

<script>
import BaseModal from "@/components/common/BaseModal.vue";
import IconCopy from "@/components/icons/IconCopy.vue";

export default {
  components: {
    IconCopy,
    BaseModal,
  },

  props: {
    isActionVisible: {
      type: Boolean,
      default: true,
    },
    item: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
    resetFormData: {
      type: Function,
      required: true,
    },
    parseFormData: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "copyItem"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the update modal window by setting the selected item to the given
     * item and calling the given callback function.
     * @param {Object} item - The item to update.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openUpdateModal(item, callback) {
      this.setSelectedItem(item)
      callback();
    },

    /**
     * Resets the form data to its initial state and calls the given callback
     * function. Used to close the modal window.
     * @param {function} callback - The function to call when the modal window is
     * closed.
     */
    closeModal(callback) {
      callback();
    },

    /**
     * Emits an 'copyItem' event with the given data and item, and calls the
     * given callback function. Used to copy an item in the table by calling
     * the 'copyItem' method of the parent component.
     *
     * @param {Object} data - The data to copy the item with.
     * @param {Object} item - The item to copy.
     * @param {function} close - The function to call when the modal window is
     * closed.
     */
    copyItem(data, item, close) {
      const parsedData = this.parseFormData(data);

      this.$emit("copyItem", item, parsedData, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
