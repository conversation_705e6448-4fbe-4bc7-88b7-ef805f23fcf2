<template>
  <!-- DataTable action that redirects to the product page of given ID -->
  <div class="icon-wrapper" title="Go to product" @click.stop="goToProduct">
    <IconGoToProduct class="icon-item" />
  </div>
</template>

<script>
import IconGoToProduct from "@/components/icons/IconGoToProduct.vue";

export default {
  components: {
    IconGoToProduct,
  },

  props: {
    productId: {
      type: [String, Number],
      required: true,
    },
  },

  methods: {
    /**
     * Navigates to the product page using the product ID.
     * It constructs the URL path dynamically based on the productId prop
     * and performs a route push to navigate to that path.
     */
    goToProduct() {
      this.$router.push(`/products/${this.productId}`);
    },
  },
};
</script>

<style lang="scss" scoped></style>
