<template>
  <!-- Non-clickable DataTable view action (actually does same thing as clicking on row) -->
  <div
    class="icon-wrapper"
    title="View"
    @click.stop="() => $emit('selectItem', item)"
  >
    <IconView class="icon-item" />
  </div>
</template>

<script>
import IconView from "@/components/icons/IconView.vue";

export default {
  components: {
    IconView,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },

  emits: ["selectItem"],
};
</script>

<style lang="scss" scoped></style>
