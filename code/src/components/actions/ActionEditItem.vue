<template>
  <!-- DataTable edit action (opens modal with options for item editing) -->
  <BaseModal
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
    v-if="isActionVisible"
  >
    <!-- Button that opens modal with options for item editing -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Edit"
        @click.stop="openUpdateModal(item, open)"
      >
        <IconEdit class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with options for item editing -->
    <template #content="{ close }">
      <slot
        name="updateActionModalContent"
        :formData="formData"
        :close="() => closeModal(close)"
        :updateItem="() => updateItem(formData, item, close)"
      />
    </template>
  </BaseModal>
</template>

<script>
import IconEdit from "@/components/icons/IconEdit.vue";
import BaseModal from "@/components/common/BaseModal.vue";

export default {
  components: {
    IconEdit,
    BaseModal,
  },

  props: {
    isActionVisible: {
      type: Boolean,
      default: true,
    },
    item: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
    resetFormData: {
      type: Function,
      required: true,
    },
    parseFormData: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "updateItem"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the update modal window by setting the selected item to the given
     * item and calling the given callback function.
     * @param {Object} item - The item to update.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openUpdateModal(item, callback) {
      this.setSelectedItem(item);
      callback();
    },

    /**
     * Resets the form data to its initial state and calls the given callback
     * function. Used to close the modal window.
     * @param {function} callback - The function to call when the modal window is
     * closed.
     */
    closeModal(callback) {
      callback();
    },

    /**
     * Emits an 'updateItem' event with the given data and item, and calls the
     * given callback function. Used to update an item in the table by calling
     * the 'updateItem' method of the parent component.
     *
     * @param {Object} data - The data to update the item with.
     * @param {Object} item - The item to update.
     * @param {function} close - The function to call when the modal window is
     * closed.
     */
    updateItem(data, item, close) {
      const parsedData = this.parseFormData(data);

      this.$emit("updateItem", item, parsedData, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
