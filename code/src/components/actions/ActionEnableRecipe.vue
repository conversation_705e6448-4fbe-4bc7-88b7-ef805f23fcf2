<template>
  <!-- DataTable recipe activation action (opens modal with confirmation for recipe enablement) -->
  <BaseModal
    v-if="recipe.disabled === 'Disabled'"
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- Button that opens modal with confirmation for recipe enablement -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Enable"
        @click.stop="openEnableModal(recipe, open)"
      >
        <IconEntityDisabled class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with confirmation for recipe enablement -->
    <template #content="{ close }">
      <v-sheet class="item-action-modal">
        <p class="item-action-modal__message">
          Are you sure you want to enable recipe?
        </p>

        <div class="form-buttons">
          <BaseButton @click="close">Close</BaseButton>
          <BaseButton
            btnType="primary"
            :loading="isUpdating"
            @click="enableRecipe(formData, recipe, close)"
          >
            Enable
          </BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconEntityDisabled from "@/components/icons/IconEntityDisabled.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconEntityDisabled,
    BaseModal,
    BaseButton,
  },

  props: {
    isUpdating: {
      type: Boolean,
      required: true,
    },
    recipe: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
    parseFormData: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "updateRecipe"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the update modal window by setting the selected item to the given
     * recipe and calling the given callback function.
     * @param {Object} recipe - The recipe to update.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openEnableModal(recipe, callback) {
      this.setSelectedItem(recipe);
      callback();
    },

    /**
     * Enables the recipe by emitting an 'updateRecipe' event with parsed
     * data and the recipe to be enabled and a callback to close the modal
     * window.
     *
     * @param {Object} data - The data to update the recipe with.
     * @param {Object} recipe - The recipe to be enabled.
     * @param {function} close - The function to call when the modal window is
     * closed.
     */
    enableRecipe(data, recipe, close) {
      const parsedData = this.parseFormData(data);

      parsedData.disabled = false;

      this.$emit("updateRecipe", recipe, parsedData, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
