<template>
  <!-- DataTable recipe disable action (opens modal with confirmation for recipe disabling) -->
  <BaseModal
    v-if="recipe.disabled === 'Enabled'"
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- But<PERSON> that opens modal with confirmation for recipe disabling -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Disable"
        @click.stop="openDisableModal(recipe, open)"
      >
        <IconEntityEnabled class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with confirmation for recipe disabling -->
    <template #content="{ close }">
      <v-sheet class="item-action-modal">
        <p class="item-action-modal__message">
          Are you sure you want to disable recipe?
        </p>

        <div class="form-buttons">
          <BaseButton @click="close">Close</BaseButton>
          <BaseButton
            btnType="primary"
            :loading="isUpdating"
            @click="disableRecipe(formData, recipe, close)"
          >
            Disable
          </BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconEntityEnabled from "@/components/icons/IconEntityEnabled.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconEntityEnabled,
    BaseModal,
    BaseButton,
  },

  props: {
    isUpdating: {
      type: Boolean,
      required: true,
    },
    recipe: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
    parseFormData: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "updateRecipe"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the update modal window by setting the selected item to the given
     * recipe and calling the given callback function.
     * @param {Object} recipe - The recipe to update.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openDisableModal(recipe, callback) {
      this.setSelectedItem(recipe);
      callback();
    },

    /**
     * Disables the recipe by emitting an 'updateRecipe' event with parsed
     * data, setting the recipe as disabled, and a callback to close the modal
     * window.
     *
     * @param {Object} data - The data to update the recipe with.
     * @param {Object} recipe - The recipe to be disabled.
     * @param {function} close - The function to call when the modal window is closed.
     */
    disableRecipe(data, recipe, close) {
      const parsedData = this.parseFormData(data);

      parsedData.disabled = true;

      this.$emit("updateRecipe", recipe, parsedData, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
