<template>
  <!-- DataTable print action (opens modal with confirmation for order printing) -->
  <BaseModal
    v-if="isActionVisible"
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- But<PERSON> that opens modal with confirmation for order printing -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Print"
        @click.stop="openPrintModal(order, open)"
      >
        <IconPrint class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with confirmation for order printing -->
    <template #content="{ close }">
      <v-sheet class="item-action-modal">
        <p class="item-action-modal__message">
          Are you sure you want to print the order?
        </p>

        <div class="form-buttons">
          <BaseButton @click="close">Close</BaseButton>
          <BaseButton
            btnType="primary"
            :loading="isPerforming"
            @click="printOrder(order, close)"
          >
            Print
          </BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconPrint from "@/components/icons/IconPrint.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconPrint,
    BaseModal,
    BaseButton,
  },

  props: {
    isPerforming: {
      type: Boolean,
      required: true,
    },
    order: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "printOrder"],

  computed: {
    /**
     * Determines if the action is visible based on the order's state.
     * The action is visible if the order's state is one of the following:
     * "READY", "ERROR_PROCESSING", "ERROR_SENDING_PLANT", "ERROR_PCC".
     *
     * @returns {Boolean} True if the action is visible, false otherwise.
     */
    isActionVisible() {
      return [
        "READY",
        "ERROR_PROCESSING",
        "ERROR_SENDING_PLANT",
        "ERROR_PCC",
      ].includes(this.order.state);
    },
  },

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the print modal window by setting the selected item to the given
     * order and calling the given callback function.
     * @param {Object} order - The order to print.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openPrintModal(order, callback) {
      this.setSelectedItem(order);
      callback();
    },

    /**
     * Emits a 'printOrder' event to notify the parent component that the order
     * should be printed. The function also closes the modal window by calling
     * the given callback function.
     * @param {Object} order - The order to print.
     * @param {function} close - The function to call to close the modal window.
     */
    printOrder(order, close) {
      this.$emit("printOrder", order, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
