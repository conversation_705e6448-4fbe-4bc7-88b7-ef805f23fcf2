<template>
  <!-- Clickable DataTable view action (opens modal with item info) -->
  <BaseModal
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- Button that opens modal with item info -->
    <template #activator="{ open }">
      <div class="icon-wrapper" title="View" @click="openViewModal(item, open)">
        <IconView class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with item info -->
    <template #content="{ close }">
      <v-sheet class="selected-item" :width="420">
        <p class="selected-item__title">Item info</p>

        <v-divider />

        <!-- Data fields of item that should be shown in modal -->
        <div
          class="selected-item__field"
          v-for="(item, index) in formData"
          :key="index"
        >
          <template v-if="item.visibility.viewAction">
            <span class="selected-item__label"> {{ item.label }}: </span>

            <span class="selected-item__value">
              {{ getItemLabel(item) }}
            </span>
          </template>
        </div>

        <!-- The slot for item preview (can differ in different tables) -->
        <slot name="viewActionContentPreview" />

        <!-- Button that closes modal -->
        <div class="form-buttons mt-2">
          <BaseButton @click="close">Close</BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconView from "@/components/icons/IconView.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconView,
    BaseModal,
    BaseButton,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
    formData: {
      type: Array,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "selectItem"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the view modal window by setting the selected item to the given
     * item and calling the given callback function. Also emits the "selectItem"
     * event with the given item.
     * @param {Object} item - The item to view.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openViewModal(item, callback) {
      this.setSelectedItem(item);
      callback();
      this.$emit("selectItem", item);
    },

    getItemLabel(item) {
      if (item.type === "select") {
        // For select fields it's better to show the labels instead of IDs
        const targetOption = item.options.find(
          (option) => option.value === item.value
        );

        return targetOption ? targetOption.text : "loading...";
      } else {
        return item.value ? item.value : "-";
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
