<template>
  <!-- DataTable delete action (opens modal to confirm action) -->
  <BaseModal
    v-if="isActionVisible"
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Delete"
        @click.stop="openDeleteModal(item, open)"
      >
        <IconDelete class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with confirmation for item deletion -->
    <template #content="{ close }">
      <v-sheet class="item-action-modal">
        <p class="item-action-modal__message">
          Are you sure you want to delete the {{ entityLabel }}?
        </p>

        <div class="form-buttons">
          <BaseButton @click="close">Close</BaseButton>
          <BaseButton
            btnType="error"
            :loading="isDeleting"
            @click="deleteItem(item, close)"
          >
            Delete
          </BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconDelete from "@/components/icons/IconDelete.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconDelete,
    BaseModal,
    BaseButton,
  },

  props: {
    isActionVisible: {
      type: Boolean,
      default: true,
    },
    entityLabel: {
      type: String,
      required: true,
    },
    isDeleting: {
      type: Boolean,
      required: true,
    },
    item: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "deleteItem"],

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the delete modal window by setting the selected item to the given
     * item and calling the given callback function.
     * @param {Object} item - The item to delete.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openDeleteModal(item, callback) {
      this.setSelectedItem(item);
      callback();
    },

    /**
     * Emits a 'deleteItem' event with the specified item and close function.
     * @param {Object} item - The item to be deleted.
     * @param {function} close - The function to call when the modal window is closed.
     */
    deleteItem(item, close) {
      this.$emit("deleteItem", item, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
