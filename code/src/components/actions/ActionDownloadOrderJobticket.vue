<template>
  <!-- DataTable download jobticket action (opens modal with confirmation for downloading of order jobticket) -->
  <BaseModal
    v-if="isActionVisible"
    @onOpenModalEvent="someModalWasOpened"
    @onCloseModalEvent="someModalWasClosed"
  >
    <!-- Button that opens modal with confirmation for downloading of order jobticket -->
    <template #activator="{ open }">
      <div
        class="icon-wrapper"
        title="Jobticket"
        @click.stop="openDownloadModal(order, open)"
      >
        <IconDownload class="icon-item" />
      </div>
    </template>

    <!-- Content of modal with confirmation for downloading of order jobticket -->
    <template #content="{ close }">
      <v-sheet class="item-action-modal">
        <p class="item-action-modal__message">
          Download jobticket for a selected order?
        </p>

        <!-- Buttons that either download jobticket or close the modal -->
        <div class="form-buttons">
          <BaseButton @click="close">Close</BaseButton>
          <BaseButton
            btnType="primary"
            :loading="isDownloading"
            @click="downloadOrderJobticket(order, close)"
          >
            Download
          </BaseButton>
        </div>
      </v-sheet>
    </template>
  </BaseModal>
</template>

<script>
import IconDownload from "@/components/icons/IconDownload.vue";
import BaseModal from "@/components/common/BaseModal.vue";
import BaseButton from "@/components/common/BaseButton.vue";

export default {
  components: {
    IconDownload,
    BaseModal,
    BaseButton,
  },

  props: {
    isDownloading: {
      type: Boolean,
      required: true,
    },
    order: {
      type: Object,
      required: true,
    },
    setSelectedItem: {
      type: Function,
      required: true,
    },
  },

  emits: ["onOpenModalEvent", "onCloseModalEvent", "downloadJobticket"],

  computed: {
    /**
     * Determines if the action is visible based on the order's state.
     * The action is visible if the order's state is one of the following:
     * "SENDING_PLANT", "ERROR_SENDING_PLANT", "RECEIVED_PLANT", "ERROR_PCC", "DONE".
     *
     * @returns {Boolean} True if the action is visible, false otherwise.
     */
    isActionVisible() {
      return [
        "SENDING_PLANT",
        "ERROR_SENDING_PLANT",
        "RECEIVED_PLANT",
        "ERROR_PCC",
        "DONE",
      ].includes(this.order.state);
    },
  },

  methods: {
    /**
     * Emits an 'onOpenModalEvent' event to notify the parent component that any
     * of the modals has been opened.
     */
    someModalWasOpened() {
      this.$emit("onOpenModalEvent");
    },

    /**
     * Emits an 'onCloseModalEvent' event to notify the parent component that any
     * of the modals has been closed.
     */
    someModalWasClosed() {
      this.$emit("onCloseModalEvent");
    },

    /**
     * Opens the download modal window by setting the selected item to the given
     * order and calling the given callback function.
     * @param {Object} order - The order to download.
     * @param {function} callback - The function to call when the modal window is
     * opened.
     */
    openDownloadModal(order, callback) {
      this.setSelectedItem(order);
      callback();
    },

    /**
     * Emits a 'downloadJobticket' event to notify the parent component that the
     * jobticket for the given order should be downloaded. The function also closes
     * the modal window by calling the given callback function.
     * @param {Object} order - The order for which the jobticket should be downloaded.
     * @param {function} close - The function to call to close the modal window.
     */
    downloadOrderJobticket(order, close) {
      this.$emit("downloadJobticket", order, close);
    },
  },
};
</script>

<style lang="scss" scoped></style>
