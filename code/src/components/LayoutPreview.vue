<template>
  <!-- Preview of layout -->
  <div class="layout-preview">
    <!-- Block with layout info -->
    <div class="layout-preview__info" v-if="layoutWidth && layoutHeight">
      <h4 class="layout-preview__header">Layout info</h4>

      <div class="layout-preview__content">
        <p class="layout-preview__item">
          <span class="layout-preview__title">Width:</span>
          {{ layoutWidth }} in
        </p>

        <p class="layout-preview__item">
          <span class="layout-preview__title">Height:</span>
          {{ layoutHeight }} in
        </p>

        <p class="layout-preview__item">
          <span class="layout-preview__title">Size:</span>
          {{ sizeInKilobytes }} KB
        </p>
      </div>
    </div>

    <!-- Image of the layout -->
    <div
      v-if="imageUploaded && layoutURL"
      class="layout-preview__image-wrapper"
    >
      <img :src="layoutURL" class="layout-preview__image" />
    </div>

    <!-- Label that is shown while loading the layout image -->
    <div
      v-if="imageUploaded && !layoutURL"
      class="layout-preview__image-loading-label"
    >
      Loading image...
    </div>
  </div>
</template>

<script>
export default {
  props: {
    layoutWidth: {
      type: Number,
      required: true,
    },
    layoutHeight: {
      type: Number,
      required: true,
    },
    sizeInKilobytes: {
      type: String,
      required: true,
    },
    imageUploaded: {
      type: Boolean,
      required: true,
      default: false,
    },
    layoutURL: {
      type: String,
      required: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.layout-preview {
  &__info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px 0;
    .layout-preview__header {
      font-size: 18px;
    }
    .layout-preview__content {
      display: flex;
      flex-direction: column;
      gap: 4px;
      .layout-preview__item {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        .layout-preview__title {
          display: flex;
          width: 50px;
          font-weight: 600;
        }
      }
    }
  }
  &__image-wrapper {
    width: 320px;
    height: 240px;
    margin: 0 auto;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    .layout-preview__image {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
  &__image-loading-label {
    padding-block: 20px;
    text-align: center;
  }
}
</style>
