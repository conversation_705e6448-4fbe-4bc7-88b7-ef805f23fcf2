<template>
  <!-- File uploader -->
  <div class="fileupload">
    <!-- Drag and drop area wrapper -->
    <div class="fileupload__dragarea dragarea">
      <!-- Drag and drop area -->
      <input
        :accept="acceptType"
        type="file"
        ref="uploadInput"
        id="uploadInput"
        :multiple="multiple"
        class="dragarea__input"
        @input="formatFiles"
      />

      <!-- Drag and drop area label -->
      <label
        v-if="isDragareaVisible"
        for="uploadInput"
        class="dragarea__label"
        @drop="dropHandler($event)"
        @dragover="dragOverHandler($event)"
      >
        <!-- Drag and drop area text -->
        <div class="dragarea__text">
          <IconUpload />
          Drag and drop files here or click to upload
        </div>

        <!-- Drag and drop area file types label -->
        <p class="dragarea__filetypes">
          Accepted files types: {{ acceptType }}
        </p>
      </label>
    </div>

    <!-- File upload progress area -->
    <div
      :class="[
        'fileupload__progressarea',
        'progressarea',
        { 'progressarea--empty': !isScrollVisible },
      ]"
    >
      <!-- File upload progress area title -->
      <p v-if="isStatusVisible" class="progressarea__title">
        {{ progressStatus }}
      </p>

      <!-- File upload progress area scroller -->
      <div
        :class="[
          'progressarea__scroller',
          { 'progressarea__scroller--visible': isScrollVisible },
        ]"
        ref="progressScroller"
      >
        <!-- File upload progress area items wrapper -->
        <div class="progress" ref="progressContainer">
          <div
            v-for="file in uploadedFiles"
            :key="file.fileName"
            class="progress__item progress-item"
          >
            <!-- Icon of file inside the progress area item -->
            <IconFile />

            <!-- Content of progress area item -->
            <div class="progress-item__content">
              <!-- Info of progress area item -->
              <div class="progress-item__info">
                <!-- Name of file inside the progress area item -->
                <span class="progress-item__name">
                  {{ file.fileName }}
                </span>

                <!-- Status of progress area item -->
                <div class="progress-item__status">
                  {{ Math.floor(file.progress) }}%
                  <IconCheckmark v-if="file.progress === 100" class="icon" />
                </div>
              </div>

              <!-- Progress bar of progress area item -->
              <div class="progress-item__bar progress-bar">
                <!-- Background of progress bar -->
                <div class="progress-bar__bg" />
                <!-- Fill of progress bar -->
                <div
                  class="progress-bar__fill"
                  :style="{ width: `${Math.floor(file.progress)}%` }"
                />
              </div>
            </div>

            <!-- Cancel button of progress area item -->
            <div v-if="clearable" class="progress-item__cancel">
              <IconCancel
                title="Delete item"
                class="icon"
                @click="$emit('deleteFile', itemId)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from "vue";
import IconUpload from "@/components/icons/IconUpload.vue";
import IconCancel from "@/components/icons/IconCancel.vue";
import IconCheckmark from "@/components/icons/IconCheckmark.vue";
import IconFile from "@/components/icons/IconFile.vue";
import { useElementSize } from "@vueuse/core";
import { useFilesStore } from "@/store/files";
import { useToast } from "vue-toastification";

export default {
  /**
   * Sets up the component's reactive references and methods for managing file upload progress
   * and input clearing. It initializes references to DOM elements for the progress container
   * and scroller, and utilizes composition API functions for element sizing. A toast instance
   * is also configured for notifications. The `clearUploadInput` method is defined to reset
   * the value of the file input element.
   */
  setup() {
    const progressContainer = ref(null);
    const progressScroller = ref(null);
    const toast = useToast();

    const { height: progressScrollerHeight } = useElementSize(progressScroller);
    const { height: progressContainerHeight } =
      useElementSize(progressContainer);

    const uploadInput = ref(null); // Define ref for upload input

    /**
     * Resets the value of the file input element, clearing the user's previous file selection.
     */
    function clearUploadInput() {
      if (uploadInput.value) {
        uploadInput.value.value = null;
      }
    }

    return {
      progressContainer,
      progressScroller,
      progressContainerHeight,
      progressScrollerHeight,
      toast,
      uploadInput,
      clearUploadInput,
    };
  },

  components: {
    IconCancel,
    IconCheckmark,
    IconFile,
    IconUpload,
  },

  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
    initialFiles: {
      type: Array,
    },
    uploadedFiles: {
      type: Array,
    },
    itemId: {
      type: String,
      default: null,
    },
    acceptType: {
      type: String,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    isDragareaHidden: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["deleteFile", "uploadFiles"],

  /**
   * Data properties of the component.
   *
   * @property {Object} useFilesStore - The store for files.
   * @property {Function} useElementSize - The function to get the size of the element.
   */
  data() {
    return {
      useFilesStore: useFilesStore(),
      useElementSize: useElementSize,
    };
  },

  computed: {
    /**
     * Computes whether the scroll bar of the progress area is visible.
     *
     * @returns {Boolean} True if the scroll bar is visible, false otherwise.
     */
    isScrollVisible() {
      return this.progressContainerHeight > this.progressScrollerHeight;
    },

    /**
     * Computes whether the drag and drop area is visible.
     *
     * @returns {Boolean} True if the drag and drop area is visible, false otherwise.
     */
    isDragareaVisible() {
      if (this.multiple && !this.isDragareaHidden) {
        return true;
      } else {
        return this.uploadedFiles.length === 0 && !this.isDragareaHidden
          ? true
          : false;
      }
    },

    /**
     * Computes whether the status area is visible.
     *
     * @returns {Boolean} True if the status area is visible, false otherwise.
     */
    isStatusVisible() {
      return !this.isDragareaHidden;
    },

    /**
     * Computes a string to be displayed in the status area of the file uploader.
     * If the user has uploaded files, it displays the number of uploaded files out
     * of the total number of files. If the user didn't upload any files, it
     * displays a message indicating that.
     *
     * @returns {String} A string for the status area of the file uploader.
     */
    progressStatus() {
      if (this.uploadedFiles && this.uploadedFiles.length) {
        const uploaded = this.uploadedFiles.filter(
          (file) => file.progress === 100
        ).length;

        return `${uploaded} out of ${this.uploadedFiles.length} uploaded`;
      } else {
        return "You didn't upload any files yet";
      }
    },
  },

  methods: {
    /**
     * Formats the files from an input event to an array of File objects
     * and emits the "uploadFiles" event with the files and the id of the
     * item being edited.
     *
     * @async
     * @param {Event} event - The input event.
     */
    async formatFiles(event) {
      const target = event.target;
      const files = Array.from(target.files);

      this.$emit("uploadFiles", files, this.itemId);
    },

    /**
     * Handles the drop event of the file uploader. It prevents the default
     * drag and drop behavior, extracts the files from the event, filters them
     * by type, and emits the "uploadFiles" event with the files and the id of the
     * item being edited.
     *
     * @param {DragEvent} ev - The drop event.
     */
    dropHandler(ev) {
      ev.preventDefault();

      if (ev.dataTransfer?.items) {
        const files = [];

        [...ev.dataTransfer.items].forEach(async (item) => {
          if (item.kind === "file" && item.type === this.acceptType) {
            const file = item.getAsFile();

            files.push(file);
          } else if (item.kind === "file" && item.type !== this.acceptType) {
            this.toast.warning("Invalid file type!", { timeout: 2000 });
          }
        });
        this.$emit("uploadFiles", files, this.itemId);
      }
    },

    /**
     * Handles the dragover event of the file uploader. Prevents the default
     * drag and drop behavior.
     *
     * @param {DragEvent} ev - The dragover event.
     */
    dragOverHandler(ev) {
      ev.preventDefault();
    },
    reset() {
      this.clearUploadInput();
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  font-family: "Inter", sans-serif;
}

.fileupload {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #ffffff;
  width: auto;
  max-width: 420px;
  margin-bottom: 22px;

  &__buttons {
    display: flex;
    gap: 8px;
  }
}

.dragarea {
  &__input {
    display: none;
  }

  &__label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
    background: #f6f6f6;
    border: 1px solid rgb(118, 118, 118);
    border-style: dashed;
    padding: 12px;
    cursor: pointer;
  }

  &__text {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(0, 0, 0, 0.87);
    font-size: 14px;
  }

  &__filetypes {
    color: rgba(0, 0, 0, 0.87);
    font-size: 12px;
  }
}

.progressarea {
  display: flex;
  flex-direction: column;
  gap: 16px;

  &__title {
    font-size: 14px;
    color: #1976d2;
    margin-bottom: 4px;
  }

  &__scroller {
    max-height: 188px;
    overflow-y: auto;
    scrollbar-color: rgb(118, 118, 118) #f6f6f6;
    scrollbar-width: thin;

    &--visible {
      padding-right: 10px;
    }
  }

  &--empty {
    gap: 0;
  }
}

.progress {
  display: flex;
  flex-direction: column;
  gap: 32px;

  &-item {
    position: relative;
    display: flex;
    width: 100%;
    min-height: 36px;

    &:not(:last-child)::after {
      position: absolute;
      content: "";
      bottom: -16px;
      left: 0;
      background: rgb(118, 118, 118);
      height: 1px;
      width: 100%;
    }

    &__cancel {
      display: flex;
      height: 22.4px;
      align-items: center;
    }

    &__content {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin: 0 8px 0 4px;
      gap: 8px;
    }

    &__info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    &__name {
      display: flex;
      max-width: 220px;
      overflow: hidden;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.87);
      cursor: pointer;
    }

    &__status {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.87);
    }
  }

  &-bar {
    display: flex;
    position: relative;
    width: 100%;
    height: 5px;

    &__bg {
      width: 100%;
      height: 5px;
      background: #aeccf6;
    }

    &__fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: #1976d2;
    }
  }
}

.fileupload__button {
  padding: 4px 16px;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: 0.1s ease;

  &--proceed {
    color: #ffffff;
    background: #1976d2;
    &:hover {
      background: #1562af;
    }
  }

  &--abort {
    color: #ffffff;
    background: #dc3545;
    &:hover {
      background: #bb2d3b;
    }
  }
}

.icon {
  min-width: 20px;
  min-height: 20px;
  max-width: 20px;
  max-height: 20px;
  cursor: pointer;
}

::-webkit-scrollbar {
  height: 6px;
  width: 4px;
  cursor: pointer;
}

::-webkit-scrollbar-track {
  background: #f6f6f6;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(118, 118, 118);
  cursor: pointer;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(118, 118, 118);
}
</style>
