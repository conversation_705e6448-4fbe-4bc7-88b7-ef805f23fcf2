<template>
  <!-- Sidebar with navigation -->
  <v-navigation-drawer
    disable-resize-watcher
    disable-route-watcher
    :modelValue="isOpen"
    :scrim="false"
    persistent
  >
    <v-list>
      <!-- List of navigation items -->
      <v-list-item
        v-for="(item, index) in items"
        :key="index"
        @click="navigateToPage(item.link)"
        :class="{ 'v-list-item--active': item.value === currentRoute }"
      >
        <v-list-item-title>{{ item.title }}</v-list-item-title>
      </v-list-item>
    </v-list>

    <!-- Bottom part of the sidebar -->
    <div class="sidebarBottomContainer">
      <!-- BHS logo -->
      <v-img
        class="logo"
        :src="logoSource"
        height="100px"
        width="100px"
        @mousedown="startLongPress"
        @mouseup="endLongPress"
        @mouseleave="endLongPress"
      />
      <!-- Footer links -->
      <v-list class="sidebarLinkWrapper">
        <!-- Manual -->
        <v-list-item
          class="sidebarFooterLink"
          @click="popup()"
          v-tooltip:top="'Manual'"
        >
          <IconBook style="width: 24px; height: 24px" />
        </v-list-item>

        <!-- API -->
        <v-list-item
          class="sidebarFooterLink"
          @click="redirectToSwagger"
          v-if="showSwaggerLink"
          v-tooltip:top="'Swagger'"
        >
          <IconApi style="width: 24px; height: 24px" />
        </v-list-item>

        <!-- ChangeLog -->
        <v-list-item
          class="sidebarFooterLink"
          @click="navigateToPage('/changelog')"
          v-tooltip:top="'ChangeLog'"
        >
          <IconRadar style="width: 24px; height: 24px" />
        </v-list-item>

        <!-- About -->
        <v-list-item
          class="sidebarFooterLink"
          @click="navigateToPage('/about')"
          v-tooltip:top="'About'"
        >
          <IconInformation style="width: 24px; height: 24px" />
        </v-list-item>
      </v-list>
    </div>
  </v-navigation-drawer>
</template>

<script>
import { useHelperStore } from "@/store/helper";
import IconBook from "@/components/icons/IconBook.vue";
import IconInformation from "@/components/icons/IconInformation.vue";
import IconApi from "@/components/icons/IconApi.vue";
import IconRadar from "@/components/icons/IconRadar.vue";
// Import Logo images
import logoAluminium from "@/assets/BHS-Logo_aluminium.svg";
import logoRed from "@/assets/BHS-Logo_red.svg";
import IconCancel from "@/components/icons/IconCancel.vue";
import IconDownload from "@/components/icons/IconDownload.vue";

export default {
  components: {
    IconDownload,
    IconCancel,
    IconBook,
    IconInformation,
    IconApi,
    IconRadar,
  },
  /**
   * The component's local state.
   *
   * @type {Object}
   * @property {string} name - The name of the component.
   * @property {boolean} drawer - Whether the drawer is open or not.
   * @property {Array<Object>} items - The items in the sidebar.
   * @property {string} items.title - The title of the item.
   * @property {string} items.value - The value of the item.
   * @property {string} items.link - The link of the item.
   */
  data: () => ({
    name: "AppSidebar",
    drawer: false,
    items: [
      {
        title: "Products",
        value: "products",
        link: "/products",
      },
      {
        title: "Orders",
        value: "orders",
        link: "/orders",
      },
      {
        title: "Customers",
        value: "customers",
        link: "/customers",
      },
      {
        title: "Recipes",
        value: "recipes",
        link: "/recipes",
      },
    ],
    showText: "",
  }),

  props: {
    isOpen: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    /**
     * Returns the name of the current route.
     *
     * @return {string} The name of the current route.
     */
    currentRoute() {
      return this.$router.currentRoute.value.name;
    },

    /**
     * Returns true if the swagger link should be shown in the sidebar based on the current environment.
     *
     * @return {boolean} Whether the swagger link should be shown in the sidebar.
     */
    showSwaggerLink() {
      return (
        import.meta.env.VITE_ENV === "development" ||
        import.meta.env.VITE_ENV === "staging"
      );
    },
    /**
     * Returns the source of the logo based on Debug Mode State.
     */
    logoSource() {
      return useHelperStore().devDebug ? logoRed : logoAluminium;
    },
  },

  methods: {
    popup() {
      let route = this.$router.resolve({ path: "/docs" });
      window.open(route.href, "_blank");
    },
    /**
     * Navigates to the given page by pushing the given link to the router.
     *
     * @param {string} link The link to navigate to.
     */
    navigateToPage(link) {
      this.$router.push(link);
    },

    /**
     * Opens the swagger page in a new tab.
     *
     * This link is only shown in the development and staging environments.
     */
    redirectToSwagger() {
      window.open(
        `https://swagger.${import.meta.env.VITE_ROOT_DOMAIN}`,
        "_blank"
      );
    },

    /**
     * Starts the long press timer to toggle Debug.
     */
    startLongPress() {
      this.longPressTimer = setTimeout(() => {
        const store = useHelperStore();
        store.toggleDevDebug();
      }, 3000); // 3 Seconds
    },
    /**
     * Ends the long press timer to toggle Debug.
     */
    endLongPress() {
      clearTimeout(this.longPressTimer);
    },
  },
};
</script>

<style lang="scss" scoped>
.sidebarFooterLink {
  position: relative;
  margin-bottom: 5px;

  * {
    font-size: 12px;
  }

  .scale-icon {
    transition: transform 0.3s;
  }

  &:hover .scale-icon {
    transform: scale(1.1);
  }
}

.v-navigation-drawer {
  background-color: #3d3e40 !important;
}

.v-list {
  padding: 0 !important;
}

.v-list-item-title {
  font-weight: bold !important;
  color: white !important;
}

.v-list-item:hover,
.v-list-item--active {
  background: #666666 !important;
}
</style>
<style lang="scss">
.v-navigation-drawer__content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .sidebarBottomContainer {
    display: flex;
    flex-direction: column;
    align-items: center;

    .logo {
      align-self: center;
      flex: unset !important;

      .v-responsive__sizer {
        display: none;
      }
    }

    .sidebarLinkWrapper {
      width: 90%;
      display: flex;
      justify-content: space-around;
    }

    .sidebarFooterLink {
      margin-bottom: 5px;
      color: #afafb0 !important;

      * {
        font-size: 12px;
      }
    }
  }
}
</style>
