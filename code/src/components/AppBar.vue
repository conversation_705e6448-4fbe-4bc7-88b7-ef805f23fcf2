<template>
  <!-- Main header of the application -->
  <v-app-bar app dark :style="debugToggle">
    <!-- Left Buttons -->
    <template v-for="(button, index) in leftButtons" :key="index">
      <v-btn :icon="button.icon" @click="button.callback">
        <v-icon>{{ button.icon }}</v-icon>
      </v-btn>
    </template>

    <!-- Title -->
    <v-app-bar-title>{{ title }}</v-app-bar-title>

    <v-spacer />

    <!-- Right Buttons -->
    <template v-for="(button, index) in rightButtons" :key="index">
      <v-btn :icon="button.icon" @click="button.callback">
        <v-icon>{{ button.icon }}</v-icon>
      </v-btn>
    </template>
  </v-app-bar>
</template>

<script setup>
import { computed } from "vue";
import { useHelperStore } from "@/store/helper";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  rightButtons: {
    type: Array,
    default: () => [],
  },
  leftButtons: {
    type: Array,
    default: () => [],
  },
});

const debugToggle = computed(() => ({
  backgroundColor: useHelperStore().devDebug
    ? "#dc2626 !important"
    : "#3d3e40 !important",
  borderBottom: useHelperStore().devDebug
    ? "2px solid #dc2626 !important"
    : "2px solid #459df6 !important",
}));

const emit = defineEmits(["toggleSidebar"]);

/**
 * Logs out the user by redirecting to the logout endpoint.
 * This action may trigger a special process in the edge lambda
 * to handle the user's logout session.
 */
const logout = () => {
  // That will trigger a special action in our edge lambda and logout the user
  location.href = "/logout";
};
</script>

<style scoped lang="scss">
.v-toolbar-title {
  color: #e1e1e1 !important;
  font-weight: bold !important;
}

.v-btn {
  color: #e1e1e1 !important;
  border-radius: 0;

  &:hover {
    background-color: #459df6 !important;
  }
}
</style>
