<template>
  <!-- Timeline layout -->
  <div ref="timelineContainer" class="timeline">
    <!-- Timeline items (each item has: id, text, line) -->
    <div
      v-for="item in timelineItems"
      :key="item.id"
      :class="[
        'timeline__item timeline-item',
        { 'timeline-item_active': item.id <= step },
      ]"
    >
      <span class="timeline-item__step" @click="$emit('setTab', item.id)">
        {{ item.id }}
      </span>

      <div class="timeline-item__text" @click="$emit('setTab', item.id)">
        {{ item.text }}
      </div>

      <div class="timeline-item__line" />
    </div>
  </div>
</template>

<script setup>
import { useElementSize } from "@vueuse/core";
import { computed, ref } from "vue";

const props = defineProps({
  step: Number,
  firstField: String,
  secondField: String,
});

defineEmits(["setTab"]);

const timelineContainer = ref(null);
const { width } = useElementSize(timelineContainer);

const timelineLine = computed(() => {
  if (width.value) {
    // width of a timeline is equal to the size of a container minus width of two step elements plus two additional pixels
    return width.value - 64 + 2 + "px";
  } else {
    return "100%";
  }
});

const timelineItems = computed(() => {
  return [
    { id: 1, text: props.firstField },
    { id: 2, text: props.secondField },
  ];
});
</script>

<style lang="scss" scoped>
.timeline-layout {
  margin-bottom: 16px;
  background: transparent;
  box-shadow: none;
  padding: 0;
}

.timeline {
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: white;
  border-radius: 4px;

  .timeline-item .timeline-item__line {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    transform: translateY(-50%);
    height: 4px;
    width: 24px;
    background: #d6d6d6;
  }

  .timeline-item:first-child {
    .timeline-item__line {
      display: none;
      background: #1976d2;
    }
  }

  .timeline-item:not(:first-child) {
    .timeline-item__line {
      left: calc(0 - v-bind(timelineLine));
    }
  }

  .timeline-item_active {
    .timeline-item__description {
      background: #1976d2;
      color: #d6d6d6;
    }

    &:not(:last-child) .timeline-item__line {
      background: #1976d2;
    }
  }
}

.timeline-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-grow: 0;
  position: relative;

  &:first-child {
    align-items: flex-start;

    .timeline-item__text {
      text-align: left;
    }
  }

  &:not(:first-child) {
    align-items: flex-end;

    .timeline-item__text {
      text-align: right;
    }
  }

  &__step {
    min-height: 32px;
    min-width: 32px;
    border-radius: 50%;
    background: #d6d6d6;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #666666;
    font-size: 16px;
    position: relative;
    margin-bottom: 8px;
    transition: 0.1s ease;
    z-index: 2;
  }

  &__text {
    font-size: 14px;
    width: 120px;
    color: #666666;
  }

  &__description {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    background: #666666;
    border-radius: 10px;
    width: 210px;
    text-align: center;
    font-size: 16px;
    flex-grow: 1;
    line-height: 1.2;
  }

  &:not(:first-child) {
    .timeline-item__line {
      content: "";
      position: absolute;
      top: 18px;
      right: 32px;
      transform: translateY(-50%);
      height: 4px;
      width: v-bind(timelineLine);
      background: #d6d6d6;
      transition: 0.1s ease;
    }
  }

  &:first-child {
    .timeline-item__description {
      background: #1976d2;
      color: #d6d6d6;
    }

    .timeline-item__step {
      background: #1976d2;
      color: #d6d6d6;
    }
  }
}

.timeline-item.timeline-item_active {
  .timeline-item__text {
    color: #1976d2;
  }

  .timeline-item__step {
    background: #1976d2;
    color: #ffffff;
  }

  &:not(:first-child) .timeline-item__line {
    background: #1976d2;
    // box-shadow: inset 3000px 0 0 0 var(#1976D2);
  }
}
</style>
