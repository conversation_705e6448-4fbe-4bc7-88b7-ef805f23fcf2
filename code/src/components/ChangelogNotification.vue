<!-- src/components/ChangelogNotification.vue -->
<template>
  <div v-if="showNotification" class="changelogNotificationWrapper">
    <div class="changeLogNotificationCard">
      <button class="closeButton" @click="closeNotification">×</button>
      <h1>New Changelog</h1>
      <p>
        The application was updated.<br />View the changelog to see what's new.
      </p>
      <button @click="navigateToChangelog">View Changelog</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";

const router = useRouter();
const showNotification = ref(false);
let currentVersion = "";

/**
 * Closes the notification.
 */
const closeNotification = () => {
  showNotification.value = false;
  localStorage.setItem("lastChangelogViewed", true);
  localStorage.setItem("lastChangelog", currentVersion);
};

/**
 * Navigates to the changelog page.
 */
const navigateToChangelog = () => {
  closeNotification();
  router.push("/changelog");
};


onMounted(() => {
  axios.get("/changelog.md")
    .then((response) => {
      const data = response.data;
      const versionMatch = data.match(
        /\[version\]: # (v\d+\.\d+\.\d+(?:-\w+)*)/
      );
      if (versionMatch) {
        const version = versionMatch[1];

        currentVersion = version;

        const lastChangelog = localStorage.getItem("lastChangelog");
        const lastChangelogViewed = localStorage.getItem("lastChangelogViewed");

        if (lastChangelog != version || !lastChangelogViewed) {
          showNotification.value = true;
        }
      }
    })
    .catch((error) => {
      console.error("There was a problem with the axios operation:", error);
    });
});
</script>

<style scoped lang="scss">
.changelogNotificationWrapper {
  position: fixed;
  display: flex;
  background-color: rgba(40, 40, 40, 0.8);
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: 9999;
}

.changeLogNotificationCard {
  display: flex;
  position: relative;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  margin: auto;
  background-color: #005094;
  color: white;
  padding: 1rem;
  height: 100%;
  width: 100%;
  max-height: 300px;
  max-width: 400px;
  padding: 2rem;
  box-shadow: 0px 2px 20px -15px #000000;
  border-radius: 10px;

  .closeButton {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
  }

  button {
    background-color: rgb(12 69 123);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
  }

  p {
    font-size: 1.2rem;
  }
}
</style>
