body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    padding: 0 1rem;
    box-sizing: border-box;
}

.card {
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px 40px;
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.logo {
    width: auto;
    max-height: 175px;
    margin-bottom: 30px;
}

h1 {
    font-size: 5em;
    margin: 0;
    color: #005094;
}

.message {
    font-size: 1.3em;
    margin: 20px 0;
    text-transform: uppercase;
}

.information {
    font-size: 1.3em;
    margin: 20px 0;
}

.contact {
    margin-top: 30px;
    font-size: 1.1em;
    font-style: italic;
}

.contact a {
    color: black;
    text-decoration: underline;
}

.contact a:hover {
    text-decoration: underline;
}

a.btn {
    margin-top: 24px;
    padding: 10px 24px;
    font-size: 1em;
    border: none;
    border-radius: 6px;
    background: #1976d2;
    color: #fff;
    cursor: pointer;
    min-width: 120px;
    display: inline-block;
    margin-right: 12px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

a.btn:hover {
    background-color: #005094;
}

a.btn:last-child {
    margin-right: 0;
}

@media (max-width: 400px) {
    .card {
        padding: 16px 4px;
        max-width: 98vw;
        max-height: 98vh;
    }
    .logo {
        max-height: 80px;
        margin-bottom: 20px;
    }
    h1 {
        font-size: 2.5em;
    }
    a.btn {
        font-size: 0.95em;
        padding: 8px 10px;
    }
}
