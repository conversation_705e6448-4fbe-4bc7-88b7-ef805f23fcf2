<!-- [version]: # v1.4.0 -->

## v1.4.0

#### 2025-07-24

<br>

**Auto**
- Order Number: now under text
- Order Number: example content filed added for better result estimation

**VDP**
- Real preview barcodes shown now in Editor
- VDP now shown in preview
- Example content field added for better result estimation

**Proofscope**
- Layers with VDP shown now

**API**
- Owner of API created products now shown in print informations

<br>

## v1.3.0

#### 2025-06-12

<br>

- Corrected printer names in orders table view.
- Improved layout in product view.
- Fixed filter breadcrumb labels.
- Cleared selectors in the order creation form.
- Fixed wrong ink coverage in printable info.
- Fixed paper type in printable info.
- Corrected filters in recipes, orders, and customer tables.
- Fixed orders loading for previous and next products on the product view page.
- Added recipe selector in create and edit product forms.
- Cleared the selected layout when closing the modal.
- Implemented local caching of images.
- Implemented Auto Codes
- Fixed order creation via API without recipe ID

<br>

## v1.2.6

#### 2025-05-19

<br>

- Add "delete product" button to product page
- Now able to re-upload the layout for an existing product
- Now able to copy products
- Fixed order creation via API

## v1.2.5

#### 2025-05-08

<br>

**Recipes**

- got an own menu point now
- can be disabled now
- recipes can now be selected per order

**Products**

- can be deleted now
- limit product names to 50 characters
- product names must be unique now
- live updates from server (automatic data refresh)

**Customer**

- limited customer values length
  - name: 50 characters
  - description: 100 characters
  - local address: 70 characters
  - mail contact: 50 characters
  - customer id: 25 characters

**Approval**

- approvals can be opened directly in portal now (no more email)

<br>

## v1.2.0

#### 2025-04-01

<br>

- Easter Egg Color Switch
- Storing update timestamp
- CSV separators change from , to ;
- Added 'plant name' column for tables & filters
- More robust file upload
- Added state icon on product details
- Customer and recipes are now searchable by typing in field
- Timeformat change to local (pm/am for usa, 24h for germany)
- Full Preview PDF now downloadable via "Action Menu" -> "Download Package Layout"
- Refactored wording
- Added customer API for product creation
- Optimized initial page load times
- Printable Product & Order information added
- Added stage scrollbars which are synchronized with grabbing
- Changed zoom icons for fitting height & width
- New VDP barcode placeholder
- Added customer required vdp barcode size

<br>

## v1.1.0

<br>

- Layout dimensions now display up to four decimal places
- Artwork preflight no longer requires approval if no changes were made
- "Brandowner" has been renamed to "Customer"
- Zoom factor now displayed in the Editor toolbar
- Product editing functionality has been added
- Communication errors with plant or printer are now displayed
- Reprinting of failed orders is now supported
- Order number input is now restricted to valid values
- Manual page has been added for reference
- Fixed an issue where the editor canvas would move while rotating an object
- Corrected positioning of cut marks in each zoom for each layout format
- All dialogs can now be closed with the ESC key or by clicking on the background

<br>

## v1.0.6

<br>

- Added edit button to product page
- Improved how product owner and approver are displayed
- Bug fixes

<br>

## v1.0.5

<br>

- Bottom triangle mark in editor now optional
- Owner name now shown in product details
- Modular windows now closeable with ESC
- https://packsuite.io now reachable
- Editor has now a background color
- Error about missing thumbnail removed

<br>

## v1.0.2

<br>

- Minor Hotfixes

<br>

## v1.0.1

<br>

- Fixed placeholder for machine QR code on the overlay
- Fixed snaplines for marks
- Made cut mark triplex optional
- Fixed issue with full-size thumbnail display

<br>

## v1.0.0

<br>

- Updated the changelog
- Fixed product thumbnail size and ratio
- Added layout lightbox feature
- Fixed missing lightbox package
- Refactored text and styles for info boxes
- Updated Cloudconnector
- Updated preflight option
- Added disable brandowners feature
- Initialized UUID for customer order and product IDs
- Added lambda security
- Added GET endpoint for listing all DPUs
- Updated workflow lambda
- Limited random generated order number to 20 characters
- Renamed labels and fields
- Added maintenance page
- Added changelog pipeline
- Updated swagger
- Updated workflows
- Updated product and order numbers
- Added data filter feature
- Added complex table expandable filters
- Updated print workflow
- Added recipes to PostgreSQL database
- Refactored actions logic from DataTable to pages via slots
- Updated orders actions in ProductDetails and OrdersPage
- Limited new schema order number
- Configured cross-account EFS access
- Split actions requests from fetching; refactored modals
