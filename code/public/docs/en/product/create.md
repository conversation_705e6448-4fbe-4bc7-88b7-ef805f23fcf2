# Create Product

<br>

## Open Editor
    1. Click "Create Product"



<br>
<img src="../../../docs_assets/create products.png" alt="create customers" width="800" />

<br>
<br>
<br>
<br>

## Provide product data
    2. Name: Give the product a name, which makes it easy to find it again
    3. Customer: Select the customer (you can search by start typing the name)
    4. Papertype: Select the paper type (you can search by start typing the papertype)
    5. Product Number: Use the same number which you use in your system for the product. This number will be used to create orders via API.
    If you don‘t have a internal product number already, you can leave the prefilled order number and use this for creating print orders via API
    6. Submit
<br>
<img src="../../../docs_assets/product form.png" alt="product form" width="400" />

<br>
<br>
<br>
<br>

## Initialize layout upload
    7. press "Upload layout" on product detail page
<br>
<img src="../../../docs_assets/start upload layout.png" alt="start upload layout" width="800" />

<br>
<br>
<br>
<br>

## Upload layout
    8. Select digital print sheet (100% PDF) for upload
    9. Check if the file is the right one. Check also width and height
    10. Click "Approve and Upload"
<br>
<img src="../../../docs_assets/upload layout.png" alt="upload layout" width="400" />

<br>
<br>
<br>
<br>

## Product is created
    11. You see the new product in the product overview now. It‘s in preflight state (airplane symbol). Wait for the approval e-mail.
